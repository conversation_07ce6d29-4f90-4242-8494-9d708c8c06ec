"""
MCP 智慧機械手臂控制系統 - 2D Pygame 模擬器
提供簡單的 2D 視覺化機械手臂模擬，用於快速測試和展示
"""

import math
import asyncio
import pygame
import numpy as np
from typing import Dict, List, Tuple, Optional
from loguru import logger
from .config import get_config

class Joint:
    """關節類別"""
    
    def __init__(self, name: str, length: float, angle: float = 0.0, 
                 min_angle: float = -180.0, max_angle: float = 180.0):
        self.name = name
        self.length = length
        self.angle = angle  # 當前角度（度）
        self.target_angle = angle  # 目標角度
        self.min_angle = min_angle
        self.max_angle = max_angle
        self.moving = False
        self.move_speed = 90.0  # 度/秒
    
    def set_target_angle(self, angle: float):
        """設定目標角度"""
        self.target_angle = max(self.min_angle, min(self.max_angle, angle))
        self.moving = abs(self.target_angle - self.angle) > 0.1
    
    def update(self, dt: float):
        """更新關節角度"""
        if self.moving:
            diff = self.target_angle - self.angle
            if abs(diff) < 0.1:
                self.angle = self.target_angle
                self.moving = False
            else:
                direction = 1 if diff > 0 else -1
                self.angle += direction * self.move_speed * dt
                self.angle = max(self.min_angle, min(self.max_angle, self.angle))

class ArmSimulator2D:
    """2D 機械手臂模擬器"""
    
    def __init__(self, width: int = 800, height: int = 600):
        self.config = get_config()
        self.width = width
        self.height = height
        self.running = False
        
        # 初始化 Pygame
        pygame.init()
        self.screen = pygame.display.set_mode((width, height))
        pygame.display.set_caption("MCP 智慧機械手臂 - 2D 模擬器")
        self.clock = pygame.time.Clock()
        
        # 顏色定義
        self.colors = {
            'background': (30, 30, 30),
            'arm': (100, 150, 255),
            'joint': (255, 100, 100),
            'target': (100, 255, 100),
            'text': (255, 255, 255),
            'grid': (60, 60, 60)
        }
        
        # 字體
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 18)
        
        # 建立關節
        self.joints = self._create_joints()
        self.base_position = (width // 2, height - 100)
        
        # 狀態資訊
        self.current_task = None
        self.status_text = "系統就緒"
        
        logger.info("✅ 2D 機械手臂模擬器初始化完成")
    
    def _create_joints(self) -> List[Joint]:
        """建立機械手臂關節"""
        joints = [
            Joint("base", 0, 0, -180, 180),           # 底座旋轉
            Joint("shoulder", 120, 0, -90, 90),       # 肩膀
            Joint("elbow", 100, 0, -135, 135),        # 手肘
            Joint("wrist", 80, 0, -90, 90),           # 手腕
            Joint("gripper", 20, 0, -45, 45)          # 夾爪
        ]
        return joints
    
    async def move_joint(self, joint_name: str, angle: float, duration: float = 1.0):
        """移動指定關節"""
        joint = self._find_joint(joint_name)
        if joint:
            joint.set_target_angle(angle)
            joint.move_speed = abs(angle - joint.angle) / duration if duration > 0 else 90.0
            
            logger.info(f"🎯 移動關節 {joint_name}: {joint.angle:.1f}° → {angle:.1f}° ({duration:.1f}s)")
            
            # 等待移動完成
            while joint.moving:
                await asyncio.sleep(0.01)
        else:
            logger.warning(f"⚠️ 未找到關節: {joint_name}")
    
    def _find_joint(self, name: str) -> Optional[Joint]:
        """尋找關節"""
        for joint in self.joints:
            if joint.name == name:
                return joint
        return None
    
    def update(self, dt: float):
        """更新模擬器狀態"""
        # 更新所有關節
        for joint in self.joints:
            joint.update(dt)
    
    def render(self):
        """渲染畫面"""
        # 清除畫面
        self.screen.fill(self.colors['background'])
        
        # 繪製網格
        self._draw_grid()
        
        # 繪製機械手臂
        self._draw_arm()
        
        # 繪製資訊面板
        self._draw_info_panel()
        
        # 更新顯示
        pygame.display.flip()
    
    def _draw_grid(self):
        """繪製背景網格"""
        grid_size = 50
        for x in range(0, self.width, grid_size):
            pygame.draw.line(self.screen, self.colors['grid'], (x, 0), (x, self.height))
        for y in range(0, self.height, grid_size):
            pygame.draw.line(self.screen, self.colors['grid'], (0, y), (self.width, y))
    
    def _draw_arm(self):
        """繪製機械手臂"""
        positions = self._calculate_joint_positions()
        
        # 繪製手臂連桿
        for i in range(len(positions) - 1):
            pygame.draw.line(self.screen, self.colors['arm'], 
                           positions[i], positions[i + 1], 8)
        
        # 繪製關節
        for i, (pos, joint) in enumerate(zip(positions, self.joints)):
            color = self.colors['joint'] if joint.moving else self.colors['arm']
            pygame.draw.circle(self.screen, color, pos, 12)
            
            # 繪製關節標籤
            label = self.small_font.render(f"{joint.name}", True, self.colors['text'])
            self.screen.blit(label, (pos[0] - 20, pos[1] - 30))
    
    def _calculate_joint_positions(self) -> List[Tuple[int, int]]:
        """計算關節位置"""
        positions = [self.base_position]
        current_pos = self.base_position
        current_angle = 0
        
        for joint in self.joints[1:]:  # 跳過底座
            current_angle += joint.angle
            rad = math.radians(current_angle)
            
            next_x = current_pos[0] + joint.length * math.cos(rad)
            next_y = current_pos[1] - joint.length * math.sin(rad)  # Y 軸向上為負
            
            next_pos = (int(next_x), int(next_y))
            positions.append(next_pos)
            current_pos = next_pos
        
        return positions
    
    def _draw_info_panel(self):
        """繪製資訊面板"""
        panel_x = 10
        panel_y = 10
        line_height = 25
        
        # 標題
        title = self.font.render("MCP 智慧機械手臂控制系統", True, self.colors['text'])
        self.screen.blit(title, (panel_x, panel_y))
        
        # 狀態
        status = self.small_font.render(f"狀態: {self.status_text}", True, self.colors['text'])
        self.screen.blit(status, (panel_x, panel_y + 30))
        
        # 關節資訊
        y_offset = 60
        for joint in self.joints:
            angle_text = f"{joint.name}: {joint.angle:.1f}°"
            if joint.moving:
                angle_text += f" → {joint.target_angle:.1f}°"
            
            color = self.colors['target'] if joint.moving else self.colors['text']
            text = self.small_font.render(angle_text, True, color)
            self.screen.blit(text, (panel_x, panel_y + y_offset))
            y_offset += 20
        
        # 控制說明
        controls = [
            "控制說明:",
            "ESC - 退出",
            "R - 重置位置",
            "SPACE - 暫停/繼續"
        ]
        
        y_offset += 20
        for control in controls:
            text = self.small_font.render(control, True, self.colors['text'])
            self.screen.blit(text, (panel_x, panel_y + y_offset))
            y_offset += 18
    
    def handle_events(self):
        """處理事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_r:
                    self.reset_arm()
                elif event.key == pygame.K_SPACE:
                    self.status_text = "暫停" if self.status_text != "暫停" else "運行中"
    
    def reset_arm(self):
        """重置機械手臂到初始位置"""
        for joint in self.joints:
            joint.set_target_angle(0.0)
        self.status_text = "重置中..."
        logger.info("🔄 機械手臂重置到初始位置")
    
    async def run(self):
        """運行模擬器主循環"""
        self.running = True
        self.status_text = "運行中"
        
        logger.info("🎮 2D 模擬器開始運行")
        
        while self.running:
            dt = self.clock.tick(60) / 1000.0  # 轉換為秒
            
            self.handle_events()
            self.update(dt)
            self.render()
            
            await asyncio.sleep(0.001)  # 讓出控制權
        
        pygame.quit()
        logger.info("🛑 2D 模擬器已停止")
    
    def set_status(self, status: str):
        """設定狀態文字"""
        self.status_text = status
    
    def get_joint_angles(self) -> Dict[str, float]:
        """取得所有關節角度"""
        return {joint.name: joint.angle for joint in self.joints}

# 全域模擬器實例
_simulator = None

def get_simulator() -> ArmSimulator2D:
    """取得模擬器實例"""
    global _simulator
    if _simulator is None:
        _simulator = ArmSimulator2D()
    return _simulator

if __name__ == "__main__":
    # 測試 2D 模擬器
    async def test_simulator():
        print("🎮 2D 機械手臂模擬器測試")
        print("=" * 50)
        
        simulator = get_simulator()
        
        # 啟動模擬器（在背景運行）
        simulator_task = asyncio.create_task(simulator.run())
        
        # 等待一下讓模擬器啟動
        await asyncio.sleep(1)
        
        # 測試動作序列
        test_moves = [
            ("shoulder", 30, 2.0),
            ("elbow", -45, 1.5),
            ("wrist", 20, 1.0),
            ("gripper", 30, 0.5),
            ("base", 45, 2.0)
        ]
        
        for joint, angle, duration in test_moves:
            simulator.set_status(f"移動 {joint} 到 {angle}°")
            await simulator.move_joint(joint, angle, duration)
            await asyncio.sleep(0.5)
        
        simulator.set_status("測試完成")
        
        # 等待用戶關閉視窗
        await simulator_task
    
    # 執行測試
    asyncio.run(test_simulator())
