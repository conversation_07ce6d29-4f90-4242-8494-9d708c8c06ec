"""
MCP 智慧機械手臂控制系統 - 智慧任務規劃器
負責協調 LLM 規劃和機械手臂執行，提供完整的任務管理功能
"""

import uuid
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from loguru import logger
from .llm_client import get_llm_client
from .config import get_config

class TaskStatus(Enum):
    """任務狀態枚舉"""
    PENDING = "pending"      # 等待執行
    PLANNING = "planning"    # 規劃中
    EXECUTING = "executing"  # 執行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 執行失敗
    CANCELLED = "cancelled"  # 已取消

class Task:
    """任務類別"""
    
    def __init__(self, command: str, context: Optional[Dict] = None):
        self.id = str(uuid.uuid4())
        self.command = command
        self.context = context or {}
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.plan = None
        self.execution_log = []
        self.error_message = None
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "id": self.id,
            "command": self.command,
            "context": self.context,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "plan": self.plan,
            "execution_log": self.execution_log,
            "error_message": self.error_message
        }

class TaskPlanner:
    """智慧任務規劃器"""
    
    def __init__(self):
        self.config = get_config()
        self.llm_client = get_llm_client()
        self.active_tasks: Dict[str, Task] = {}
        self.task_history: List[Task] = []
        self.max_history = 100
        
        logger.info("✅ 智慧任務規劃器初始化完成")
    
    async def create_task(self, command: str, context: Optional[Dict] = None) -> Task:
        """
        建立新任務
        
        Args:
            command: 自然語言指令
            context: 額外的上下文資訊
            
        Returns:
            建立的任務物件
        """
        task = Task(command, context)
        self.active_tasks[task.id] = task
        
        logger.info(f"📝 建立新任務: {task.id} - {command}")
        return task
    
    async def plan_task(self, task_id: str) -> Dict[str, Any]:
        """
        規劃任務執行步驟
        
        Args:
            task_id: 任務 ID
            
        Returns:
            任務規劃結果
        """
        if task_id not in self.active_tasks:
            raise ValueError(f"任務不存在: {task_id}")
        
        task = self.active_tasks[task_id]
        task.status = TaskStatus.PLANNING
        task.started_at = datetime.now()
        
        try:
            logger.info(f"🧠 開始規劃任務: {task.id} - {task.command}")
            
            # 使用 LLM 進行任務規劃
            plan_result = await self.llm_client.plan_task(task.command, task.context)
            
            if plan_result["status"] == "success":
                task.plan = plan_result
                task.execution_log.append({
                    "timestamp": datetime.now().isoformat(),
                    "event": "planning_completed",
                    "message": f"LLM 規劃完成，共 {len(plan_result['actions'])} 個動作"
                })
                logger.info(f"✅ 任務規劃完成: {task.id}")
                
            else:
                task.status = TaskStatus.FAILED
                task.error_message = plan_result.get("error", "規劃失敗")
                task.execution_log.append({
                    "timestamp": datetime.now().isoformat(),
                    "event": "planning_failed",
                    "message": task.error_message
                })
                logger.error(f"❌ 任務規劃失敗: {task.id} - {task.error_message}")
            
            return plan_result
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.execution_log.append({
                "timestamp": datetime.now().isoformat(),
                "event": "planning_error",
                "message": str(e)
            })
            logger.error(f"❌ 任務規劃異常: {task.id} - {e}")
            raise
    
    async def execute_task(self, task_id: str, simulator=None) -> Dict[str, Any]:
        """
        執行任務
        
        Args:
            task_id: 任務 ID
            simulator: 模擬器實例（可選）
            
        Returns:
            執行結果
        """
        if task_id not in self.active_tasks:
            raise ValueError(f"任務不存在: {task_id}")
        
        task = self.active_tasks[task_id]
        
        if not task.plan:
            raise ValueError(f"任務尚未規劃: {task_id}")
        
        task.status = TaskStatus.EXECUTING
        
        try:
            logger.info(f"🎬 開始執行任務: {task.id} - {task.command}")
            
            # 執行動作序列
            for i, action in enumerate(task.plan["actions"]):
                logger.info(f"🎯 執行動作 {i+1}/{len(task.plan['actions'])}: {action['description']}")
                
                # 記錄動作開始
                task.execution_log.append({
                    "timestamp": datetime.now().isoformat(),
                    "event": "action_start",
                    "action_index": i,
                    "action": action
                })
                
                # 如果有模擬器，執行實際動作
                if simulator:
                    await self._execute_action(simulator, action)
                else:
                    # 模擬執行時間
                    await asyncio.sleep(action.get("duration", 1.0))
                
                # 記錄動作完成
                task.execution_log.append({
                    "timestamp": datetime.now().isoformat(),
                    "event": "action_completed",
                    "action_index": i,
                    "message": f"動作完成: {action['description']}"
                })
            
            # 任務完成
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.execution_log.append({
                "timestamp": datetime.now().isoformat(),
                "event": "task_completed",
                "message": "任務執行完成"
            })
            
            logger.info(f"✅ 任務執行完成: {task.id}")
            
            # 移動到歷史記錄
            self._archive_task(task)
            
            return {
                "status": "success",
                "task_id": task.id,
                "message": "任務執行完成",
                "execution_time": (task.completed_at - task.started_at).total_seconds()
            }
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.execution_log.append({
                "timestamp": datetime.now().isoformat(),
                "event": "execution_error",
                "message": str(e)
            })
            logger.error(f"❌ 任務執行失敗: {task.id} - {e}")
            
            return {
                "status": "error",
                "task_id": task.id,
                "error": str(e)
            }
    
    async def _execute_action(self, simulator, action: Dict[str, Any]):
        """執行單個動作"""
        try:
            # 這裡會調用實際的模擬器或硬體驅動
            joint = action.get("joint")
            angle = action.get("angle")
            duration = action.get("duration", 1.0)
            
            if hasattr(simulator, 'move_joint'):
                await simulator.move_joint(joint, angle, duration)
            else:
                # 模擬執行
                await asyncio.sleep(duration)
                
        except Exception as e:
            logger.error(f"❌ 動作執行失敗: {action} - {e}")
            raise
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """取得任務資訊"""
        return self.active_tasks.get(task_id)
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """取得所有活躍任務"""
        return [task.to_dict() for task in self.active_tasks.values()]
    
    def get_task_history(self) -> List[Dict[str, Any]]:
        """取得任務歷史"""
        return [task.to_dict() for task in self.task_history]
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        if task_id not in self.active_tasks:
            return False
        
        task = self.active_tasks[task_id]
        task.status = TaskStatus.CANCELLED
        task.execution_log.append({
            "timestamp": datetime.now().isoformat(),
            "event": "task_cancelled",
            "message": "任務已取消"
        })
        
        logger.info(f"🚫 任務已取消: {task.id}")
        self._archive_task(task)
        return True
    
    def _archive_task(self, task: Task):
        """將任務移動到歷史記錄"""
        if task.id in self.active_tasks:
            del self.active_tasks[task.id]
        
        self.task_history.append(task)
        
        # 限制歷史記錄數量
        if len(self.task_history) > self.max_history:
            self.task_history = self.task_history[-self.max_history:]

# 全域任務規劃器實例
_task_planner = None

def get_task_planner() -> TaskPlanner:
    """取得任務規劃器實例"""
    global _task_planner
    if _task_planner is None:
        _task_planner = TaskPlanner()
    return _task_planner

if __name__ == "__main__":
    # 測試任務規劃器
    async def test_planner():
        print("🎯 智慧任務規劃器測試")
        print("=" * 50)
        
        planner = get_task_planner()
        
        # 建立測試任務
        task = await planner.create_task("做一個打招呼的手勢")
        print(f"📝 建立任務: {task.id}")
        
        # 規劃任務
        plan_result = await planner.plan_task(task.id)
        print(f"🧠 規劃結果: {plan_result['status']}")
        
        # 執行任務
        if plan_result['status'] == 'success':
            exec_result = await planner.execute_task(task.id)
            print(f"🎬 執行結果: {exec_result['status']}")
        
        # 顯示任務歷史
        history = planner.get_task_history()
        print(f"📚 歷史任務數量: {len(history)}")
    
    # 執行測試
    asyncio.run(test_planner())
