"""
MCP 智慧機械手臂控制系統 - 展示選單
提供各種展示選項供用戶選擇
"""

import subprocess
import sys
import os

def print_banner():
    """顯示主橫幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    MCP 智慧機械手臂控制系統                                    ║
║                        🎊 展示選單 🎊                                          ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  🧠 Gemini 1.5 Flash LLM 智慧規劃                                            ║
║  🎮 MuJoCo 3.3.5 高精度 3D 物理模擬                                          ║
║  🤖 InMoov 11-DOF 機械手臂控制                                               ║
║  🌐 RESTful API 完整接口                                                      ║
║  ⚡ 1000Hz 高頻率物理計算                                                      ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def show_demo_menu():
    """顯示展示選單"""
    menu = """
🎯 請選擇您想要的展示：

1. 🎮 2D 機械手臂模擬器展示
   - 視覺化機械手臂動作
   - 關節控制演示
   - 動作序列展示
   
2. 🧠 LLM 智慧驗證測試
   - 證明真正的 AI 智慧
   - 創造性思維測試
   - 適應性規劃驗證
   
3. 🎨 LLM 智慧規劃展示
   - 即時智慧規劃
   - 創造性動作生成
   - 2D 視覺化配合
   
4. 🔍 系統安裝驗證
   - 檢查系統完整性
   - 驗證所有組件
   - 生成健康報告
   
5. 🌐 完整系統展示
   - 啟動 FastAPI 服務器
   - 完整功能演示
   - 端到端測試
   
0. 🚪 退出

請輸入選項編號 (0-5): """
    
    print(menu)

def run_demo(choice: str):
    """執行選擇的展示"""
    demos = {
        "1": ("quick_demo.py", "2D 機械手臂模擬器展示"),
        "2": ("simple_llm_test.py", "LLM 智慧驗證測試"),
        "3": ("demo_real_llm_planning.py", "LLM 智慧規劃展示"),
        "4": ("test_installation.py", "系統安裝驗證"),
        "5": ("complete_system_demo.py", "完整系統展示")
    }
    
    if choice not in demos:
        print("❌ 無效選擇，請輸入 0-5")
        return False
    
    script, name = demos[choice]
    
    print(f"\n🚀 啟動: {name}")
    print("=" * 60)
    print(f"💡 執行腳本: {script}")
    print("⏸️  按 Ctrl+C 可以中斷展示")
    print("=" * 60)
    
    try:
        # 檢查腳本是否存在
        if not os.path.exists(script):
            print(f"❌ 找不到展示腳本: {script}")
            return False
        
        # 執行展示腳本
        result = subprocess.run([sys.executable, script], 
                              capture_output=False, 
                              text=True)
        
        if result.returncode == 0:
            print(f"\n✅ {name} 完成")
        else:
            print(f"\n⚠️ {name} 結束 (返回碼: {result.returncode})")
        
        return True
        
    except KeyboardInterrupt:
        print(f"\n⏹️ {name} 已中斷")
        return True
    except FileNotFoundError:
        print(f"❌ 找不到 Python 解釋器或腳本文件")
        return False
    except Exception as e:
        print(f"❌ 執行 {name} 時發生錯誤: {e}")
        return False

def check_system_status():
    """檢查系統狀態"""
    print("\n🔍 檢查系統狀態...")
    
    # 檢查關鍵文件
    key_files = [
        "server/config.py",
        "server/llm_client.py", 
        "server/arm_simulator.py",
        ".env",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in key_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少關鍵文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 系統文件完整")
    
    # 檢查 .env 設定
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            env_content = f.read()
            if "AIzaSyD9-cBSVjE03NQfmhUSfkLPEWFGQIYWr3U" in env_content:
                print("✅ Gemini API 金鑰已設定")
            else:
                print("⚠️ Gemini API 金鑰可能未正確設定")
    except Exception as e:
        print(f"⚠️ 無法檢查 .env 文件: {e}")
    
    return True

def main():
    """主函數"""
    print_banner()
    
    # 檢查系統狀態
    if not check_system_status():
        print("\n❌ 系統檢查失敗，請先修復問題")
        return
    
    while True:
        show_demo_menu()
        
        try:
            choice = input().strip()
            
            if choice == '0':
                print("\n👋 感謝使用 MCP 智慧機械手臂控制系統！")
                break
            
            elif choice in ['1', '2', '3', '4', '5']:
                success = run_demo(choice)
                
                if success:
                    print("\n⏸️ 按 Enter 繼續...")
                    input()
                else:
                    print("\n❌ 展示執行失敗")
                    print("⏸️ 按 Enter 繼續...")
                    input()
            
            else:
                print("❌ 無效選擇，請輸入 0-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 再見！")
            break
        except Exception as e:
            print(f"❌ 輸入處理錯誤: {e}")

if __name__ == "__main__":
    main()
