"""
MCP 智慧機械手臂控制系統 - 完整系統展示
展示 LLM 智慧規劃、2D/3D 模擬和 API 接口的完整功能
"""

import asyncio
import requests
import json
import time
from typing import Dict, Any
from loguru import logger

class SystemDemo:
    """完整系統展示類別"""
    
    def __init__(self, server_url: str = "http://localhost:8000"):
        self.server_url = server_url
        self.session = requests.Session()
        
    def print_banner(self):
        """顯示系統橫幅"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    MCP 智慧機械手臂控制系統                                    ║
║                  基於 LLM 的自主任務規劃與 MuJoCo 3D 物理模擬平台                ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🧠 Gemini 1.5 Flash LLM 智慧規劃                                            ║
║  🎮 MuJoCo 3.3.5 高精度物理模擬                                              ║
║  🤖 InMoov 11-DOF 機械手臂                                                   ║
║  🌐 RESTful API 完整接口                                                      ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def check_server_status(self) -> bool:
        """檢查伺服器狀態"""
        try:
            response = self.session.get(f"{self.server_url}/api/health", timeout=5)
            if response.status_code == 200:
                health = response.json()
                print("✅ 伺服器狀態檢查通過")
                print(f"📊 元件狀態: {health['components']}")
                return True
            else:
                print(f"❌ 伺服器回應異常: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 無法連接到伺服器: {e}")
            print("💡 請確保伺服器已啟動: python server/main.py")
            return False
    
    def submit_task(self, command: str, simulator_type: str = "2d") -> Dict[str, Any]:
        """提交任務到系統"""
        try:
            payload = {
                "command": command,
                "simulator_type": simulator_type
            }
            
            response = self.session.post(
                f"{self.server_url}/api/task",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 任務提交失敗: {response.status_code}")
                return {"status": "error", "message": "任務提交失敗"}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 任務提交異常: {e}")
            return {"status": "error", "message": str(e)}
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """取得任務狀態"""
        try:
            response = self.session.get(f"{self.server_url}/api/task/{task_id}", timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return {"status": "error", "message": "無法取得任務狀態"}
                
        except requests.exceptions.RequestException as e:
            return {"status": "error", "message": str(e)}
    
    def wait_for_task_completion(self, task_id: str, timeout: int = 30) -> bool:
        """等待任務完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            task_status = self.get_task_status(task_id)
            
            if task_status.get("status") in ["completed", "failed", "cancelled"]:
                return task_status.get("status") == "completed"
            
            print(f"⏳ 任務執行中... ({task_status.get('status', 'unknown')})")
            time.sleep(2)
        
        print("⏰ 任務執行超時")
        return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """取得系統狀態"""
        try:
            response = self.session.get(f"{self.server_url}/api/status", timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return {"status": "error"}
                
        except requests.exceptions.RequestException as e:
            return {"status": "error", "message": str(e)}
    
    def demo_llm_creativity(self):
        """展示 LLM 創造性規劃"""
        print("\n" + "="*80)
        print("🧠 LLM 創造性智慧展示")
        print("="*80)
        
        creative_commands = [
            "做一個打招呼的手勢",
            "表達開心的情緒",
            "像機器人一樣敬禮",
            "做一個優雅的動作",
            "模擬指揮家指揮音樂"
        ]
        
        for i, command in enumerate(creative_commands, 1):
            print(f"\n🎯 測試 {i}/5: {command}")
            print("-" * 50)
            
            # 提交任務
            result = self.submit_task(command)
            
            if result.get("status") == "created":
                task_id = result["task_id"]
                print(f"📝 任務已建立: {task_id}")
                
                # 等待任務完成
                if self.wait_for_task_completion(task_id, 20):
                    # 取得最終任務資訊
                    final_status = self.get_task_status(task_id)
                    if final_status.get("plan"):
                        plan = final_status["plan"]
                        print(f"✅ LLM 規劃完成:")
                        print(f"   📋 任務描述: {plan.get('task_description', 'N/A')}")
                        print(f"   ⏱️  預估時間: {plan.get('estimated_time', 0):.1f}秒")
                        print(f"   🎬 動作數量: {len(plan.get('actions', []))}")
                        
                        # 顯示前 3 個動作
                        actions = plan.get('actions', [])[:3]
                        for j, action in enumerate(actions, 1):
                            print(f"   {j}. {action.get('description', 'N/A')}")
                        
                        if len(plan.get('actions', [])) > 3:
                            print(f"   ... 還有 {len(plan.get('actions', [])) - 3} 個動作")
                    else:
                        print("❌ 無法取得任務規劃")
                else:
                    print("❌ 任務執行失敗或超時")
            else:
                print(f"❌ 任務建立失敗: {result.get('message', 'Unknown error')}")
            
            # 短暫暫停
            time.sleep(1)
    
    def demo_adaptation(self):
        """展示 LLM 適應性"""
        print("\n" + "="*80)
        print("🔄 LLM 適應性智慧展示")
        print("="*80)
        
        adaptation_tests = [
            ("握拳", "基本動作"),
            ("快速握拳", "速度要求"),
            ("非常小心地握拳", "精度要求"),
            ("用最少的動作握拳", "效率要求"),
            ("慢慢地做優雅的握拳動作", "風格要求")
        ]
        
        for i, (command, description) in enumerate(adaptation_tests, 1):
            print(f"\n🎯 適應性測試 {i}/5: {description}")
            print(f"📝 指令: {command}")
            print("-" * 50)
            
            result = self.submit_task(command)
            
            if result.get("status") == "created":
                task_id = result["task_id"]
                
                if self.wait_for_task_completion(task_id, 15):
                    final_status = self.get_task_status(task_id)
                    if final_status.get("plan"):
                        plan = final_status["plan"]
                        print(f"✅ LLM 適應結果:")
                        print(f"   ⏱️  執行時間: {plan.get('estimated_time', 0):.1f}秒")
                        print(f"   🎬 動作數量: {len(plan.get('actions', []))}")
                        print(f"   📋 策略: {plan.get('task_description', 'N/A')}")
                else:
                    print("❌ 任務執行失敗")
            else:
                print("❌ 任務建立失敗")
            
            time.sleep(1)
    
    def demo_system_integration(self):
        """展示系統整合"""
        print("\n" + "="*80)
        print("🌐 系統整合展示")
        print("="*80)
        
        # 取得系統狀態
        status = self.get_system_status()
        if status.get("status") != "error":
            print("✅ 系統狀態:")
            print(f"   🧠 LLM 模型: {status.get('llm_model', 'N/A')}")
            print(f"   🎮 MuJoCo 版本: {status.get('mujoco_version', 'N/A')}")
            print(f"   📊 活躍任務: {status.get('active_tasks', 0)}")
            print(f"   ⚡ 物理穩定: {status.get('physics_stable', False)}")
            
            # 顯示關節位置
            joint_positions = status.get('joint_positions', {})
            if joint_positions:
                print("   🤖 關節位置:")
                for joint, angle in joint_positions.items():
                    print(f"      {joint}: {angle:.1f}°")
        else:
            print("❌ 無法取得系統狀態")
        
        # 測試直接關節控制
        print("\n🎮 直接關節控制測試:")
        test_moves = [
            ("shoulder", 30),
            ("elbow", -45),
            ("wrist", 20)
        ]
        
        for joint, angle in test_moves:
            try:
                response = self.session.post(
                    f"{self.server_url}/api/joint/move",
                    json={"joint": joint, "angle": angle, "duration": 1.0},
                    timeout=5
                )
                
                if response.status_code == 200:
                    print(f"   ✅ {joint}: {angle}°")
                else:
                    print(f"   ❌ {joint}: 控制失敗")
                    
            except Exception as e:
                print(f"   ❌ {joint}: {e}")
            
            time.sleep(0.5)
    
    def run_complete_demo(self):
        """執行完整展示"""
        self.print_banner()
        
        print("🔍 正在檢查系統狀態...")
        if not self.check_server_status():
            return
        
        print("\n🚀 開始完整系統展示")
        print("💡 這將展示 LLM 的真實智慧能力，而非預設腳本")
        
        try:
            # 1. LLM 創造性展示
            self.demo_llm_creativity()
            
            # 2. LLM 適應性展示
            self.demo_adaptation()
            
            # 3. 系統整合展示
            self.demo_system_integration()
            
            print("\n" + "="*80)
            print("🎊 完整系統展示完成！")
            print("="*80)
            print("✨ 重點成果:")
            print("   🧠 LLM 展現了真正的創造性和適應性智慧")
            print("   🎮 2D/3D 模擬器運行穩定")
            print("   🌐 API 接口功能完整")
            print("   🤖 機械手臂控制精確")
            print("\n💡 這就是未來機器人控制技術的完美展示！")
            
        except KeyboardInterrupt:
            print("\n\n⏹️  展示已中斷")
        except Exception as e:
            print(f"\n❌ 展示過程中發生錯誤: {e}")

def main():
    """主函數"""
    demo = SystemDemo()
    demo.run_complete_demo()

if __name__ == "__main__":
    main()
