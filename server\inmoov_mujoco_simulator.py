"""
MCP 智慧機械手臂控制系統 - InMoov MuJoCo 3D 物理模擬器
基於 MuJoCo 3.3.5 的高精度 3D 物理模擬，模擬真實的 InMoov 機器人
"""

import os
import time
import asyncio
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import mujoco
import mujoco.viewer
from loguru import logger
from .config import get_config

class InMoovMuJoCoSimulator:
    """InMoov MuJoCo 3D 物理模擬器"""
    
    def __init__(self):
        self.config = get_config()
        self.model = None
        self.data = None
        self.viewer = None
        self.running = False
        
        # 關節映射（MuJoCo 模型中的關節名稱）
        self.joint_names = {
            'base': 'base_joint',
            'shoulder': 'shoulder_joint', 
            'elbow': 'elbow_joint',
            'wrist': 'wrist_joint',
            'thumb': 'thumb_joint',
            'index': 'index_joint',
            'middle': 'middle_joint',
            'ring': 'ring_joint',
            'pinky': 'pinky_joint'
        }
        
        # 關節限制（度）
        self.joint_limits = {
            'base': (-180, 180),
            'shoulder': (-90, 90),
            'elbow': (-135, 135),
            'wrist': (-90, 90),
            'thumb': (-45, 90),
            'index': (-10, 90),
            'middle': (-10, 90),
            'ring': (-10, 90),
            'pinky': (-10, 90)
        }
        
        # 物理參數
        self.timestep = self.config.mujoco_physics_timestep
        self.control_frequency = 100  # Hz
        
        # 場景物件
        self.objects = {}
        
        self._initialize_simulator()
    
    def _initialize_simulator(self):
        """初始化 MuJoCo 模擬器"""
        try:
            # 建立 InMoov 機器人的 MJCF 模型
            mjcf_xml = self._create_inmoov_mjcf()
            
            # 載入模型
            self.model = mujoco.MjModel.from_xml_string(mjcf_xml)
            self.data = mujoco.MjData(self.model)
            
            # 設定物理參數
            self.model.opt.timestep = self.timestep
            
            logger.info("✅ InMoov MuJoCo 3D 模擬器初始化完成")
            logger.info(f"🔧 物理時間步: {self.timestep}s")
            logger.info(f"🤖 關節數量: {self.model.njnt}")
            logger.info(f"🎮 控制頻率: {self.control_frequency}Hz")
            
        except Exception as e:
            logger.error(f"❌ MuJoCo 模擬器初始化失敗: {e}")
            raise
    
    def _create_inmoov_mjcf(self) -> str:
        """建立 InMoov 機器人的 MJCF XML 模型"""
        mjcf_xml = """
        <mujoco model="InMoov Robot Arm">
            <compiler angle="degree" coordinate="local"/>
            
            <option timestep="0.001" gravity="0 0 -9.81"/>
            
            <asset>
                <texture name="grid" type="2d" builtin="checker" rgb1=".1 .2 .3" 
                         rgb2=".2 .3 .4" width="300" height="300"/>
                <material name="grid" texture="grid" texrepeat="8 8" reflectance=".2"/>
                <material name="arm" rgba="0.4 0.6 1.0 1"/>
                <material name="joint" rgba="1.0 0.4 0.4 1"/>
                <material name="gripper" rgba="0.8 0.8 0.8 1"/>
                <material name="egg" rgba="1.0 1.0 0.8 1"/>
            </asset>
            
            <worldbody>
                <!-- 地面 -->
                <geom name="floor" pos="0 0 0" size="2 2 0.1" type="plane" material="grid"/>
                
                <!-- 桌子 -->
                <body name="table" pos="0.5 0 0.4">
                    <geom name="table_top" type="box" size="0.4 0.3 0.02" rgba="0.8 0.6 0.4 1"/>
                    <geom name="table_leg1" pos="0.35 0.25 -0.2" type="cylinder" size="0.02 0.2" rgba="0.6 0.4 0.2 1"/>
                    <geom name="table_leg2" pos="0.35 -0.25 -0.2" type="cylinder" size="0.02 0.2" rgba="0.6 0.4 0.2 1"/>
                    <geom name="table_leg3" pos="-0.35 0.25 -0.2" type="cylinder" size="0.02 0.2" rgba="0.6 0.4 0.2 1"/>
                    <geom name="table_leg4" pos="-0.35 -0.25 -0.2" type="cylinder" size="0.02 0.2" rgba="0.6 0.4 0.2 1"/>
                </body>
                
                <!-- 雞蛋 -->
                <body name="egg" pos="0.5 0 0.45">
                    <joint name="egg_free" type="free"/>
                    <geom name="egg_geom" type="ellipsoid" size="0.025 0.025 0.035" 
                          material="egg" mass="0.06" friction="0.8 0.1 0.1"/>
                </body>
                
                <!-- InMoov 機械手臂 -->
                <body name="base" pos="0 0 0.2">
                    <geom name="base_geom" type="cylinder" size="0.08 0.05" material="joint"/>
                    
                    <!-- 底座關節 -->
                    <joint name="base_joint" type="hinge" axis="0 0 1" range="-180 180" 
                           damping="0.1" frictionloss="0.01"/>
                    
                    <!-- 肩膀連桿 -->
                    <body name="shoulder_link" pos="0 0 0.05">
                        <geom name="shoulder_geom" type="cylinder" size="0.06 0.08" material="joint"/>
                        
                        <!-- 肩膀關節 -->
                        <joint name="shoulder_joint" type="hinge" axis="0 1 0" range="-90 90"
                               damping="0.2" frictionloss="0.02"/>
                        
                        <!-- 上臂連桿 -->
                        <body name="upper_arm" pos="0.12 0 0" euler="0 0 0">
                            <geom name="upper_arm_geom" type="capsule" size="0.03 0.12" material="arm"/>
                            
                            <!-- 手肘連桿 -->
                            <body name="elbow_link" pos="0.12 0 0">
                                <geom name="elbow_geom" type="cylinder" size="0.05 0.04" material="joint"/>
                                
                                <!-- 手肘關節 -->
                                <joint name="elbow_joint" type="hinge" axis="0 1 0" range="-135 135"
                                       damping="0.15" frictionloss="0.015"/>
                                
                                <!-- 前臂連桿 -->
                                <body name="forearm" pos="0.1 0 0">
                                    <geom name="forearm_geom" type="capsule" size="0.025 0.1" material="arm"/>
                                    
                                    <!-- 手腕連桿 -->
                                    <body name="wrist_link" pos="0.1 0 0">
                                        <geom name="wrist_geom" type="cylinder" size="0.04 0.03" material="joint"/>
                                        
                                        <!-- 手腕關節 -->
                                        <joint name="wrist_joint" type="hinge" axis="0 1 0" range="-90 90"
                                               damping="0.1" frictionloss="0.01"/>
                                        
                                        <!-- 手掌 -->
                                        <body name="palm" pos="0.05 0 0">
                                            <geom name="palm_geom" type="box" size="0.04 0.03 0.01" material="gripper"/>
                                            
                                            <!-- 拇指 -->
                                            <body name="thumb" pos="0.02 -0.025 0" euler="0 0 -45">
                                                <joint name="thumb_joint" type="hinge" axis="0 0 1" range="-45 90"
                                                       damping="0.05" frictionloss="0.005"/>
                                                <geom name="thumb_geom" type="capsule" size="0.008 0.025" material="gripper"/>
                                            </body>
                                            
                                            <!-- 食指 -->
                                            <body name="index_finger" pos="0.04 -0.015 0">
                                                <joint name="index_joint" type="hinge" axis="0 0 1" range="-10 90"
                                                       damping="0.05" frictionloss="0.005"/>
                                                <geom name="index_geom" type="capsule" size="0.008 0.03" material="gripper"/>
                                            </body>
                                            
                                            <!-- 中指 -->
                                            <body name="middle_finger" pos="0.04 0 0">
                                                <joint name="middle_joint" type="hinge" axis="0 0 1" range="-10 90"
                                                       damping="0.05" frictionloss="0.005"/>
                                                <geom name="middle_geom" type="capsule" size="0.008 0.032" material="gripper"/>
                                            </body>
                                            
                                            <!-- 無名指 -->
                                            <body name="ring_finger" pos="0.04 0.015 0">
                                                <joint name="ring_joint" type="hinge" axis="0 0 1" range="-10 90"
                                                       damping="0.05" frictionloss="0.005"/>
                                                <geom name="ring_geom" type="capsule" size="0.008 0.03" material="gripper"/>
                                            </body>
                                            
                                            <!-- 小指 -->
                                            <body name="pinky_finger" pos="0.04 0.025 0">
                                                <joint name="pinky_joint" type="hinge" axis="0 0 1" range="-10 90"
                                                       damping="0.05" frictionloss="0.005"/>
                                                <geom name="pinky_geom" type="capsule" size="0.008 0.025" material="gripper"/>
                                            </body>
                                        </body>
                                    </body>
                                </body>
                            </body>
                        </body>
                    </body>
                </body>
            </worldbody>
            
            <actuator>
                <!-- 位置控制器 -->
                <position name="base_ctrl" joint="base_joint" kp="100" kv="10"/>
                <position name="shoulder_ctrl" joint="shoulder_joint" kp="150" kv="15"/>
                <position name="elbow_ctrl" joint="elbow_joint" kp="120" kv="12"/>
                <position name="wrist_ctrl" joint="wrist_joint" kp="80" kv="8"/>
                <position name="thumb_ctrl" joint="thumb_joint" kp="50" kv="5"/>
                <position name="index_ctrl" joint="index_joint" kp="50" kv="5"/>
                <position name="middle_ctrl" joint="middle_joint" kp="50" kv="5"/>
                <position name="ring_ctrl" joint="ring_joint" kp="50" kv="5"/>
                <position name="pinky_ctrl" joint="pinky_joint" kp="50" kv="5"/>
            </actuator>
        </mujoco>
        """
        return mjcf_xml
    
    async def move_joint(self, joint_name: str, angle: float, duration: float = 1.0):
        """移動指定關節"""
        if joint_name not in self.joint_names:
            logger.warning(f"⚠️ 未知關節: {joint_name}")
            return
        
        # 檢查角度限制
        min_angle, max_angle = self.joint_limits[joint_name]
        angle = max(min_angle, min(max_angle, angle))
        
        # 取得關節索引
        joint_id = mujoco.mj_name2id(self.model, mujoco.mjtObj.mjOBJ_JOINT, self.joint_names[joint_name])
        actuator_id = mujoco.mj_name2id(self.model, mujoco.mjtObj.mjOBJ_ACTUATOR, f"{joint_name}_ctrl")
        
        if joint_id < 0 or actuator_id < 0:
            logger.warning(f"⚠️ 找不到關節或控制器: {joint_name}")
            return
        
        # 轉換角度為弧度
        target_angle_rad = np.radians(angle)
        current_angle_rad = self.data.qpos[joint_id]
        
        logger.info(f"🎯 移動關節 {joint_name}: {np.degrees(current_angle_rad):.1f}° → {angle:.1f}° ({duration:.1f}s)")
        
        # 平滑移動
        steps = int(duration * self.control_frequency)
        for step in range(steps):
            progress = step / steps
            interpolated_angle = current_angle_rad + progress * (target_angle_rad - current_angle_rad)
            
            # 設定控制信號
            self.data.ctrl[actuator_id] = interpolated_angle
            
            # 執行物理模擬步驟
            mujoco.mj_step(self.model, self.data)
            
            # 控制頻率
            await asyncio.sleep(1.0 / self.control_frequency)
    
    def get_joint_angles(self) -> Dict[str, float]:
        """取得所有關節角度（度）"""
        angles = {}
        for joint_name, mujoco_name in self.joint_names.items():
            joint_id = mujoco.mj_name2id(self.model, mujoco.mjtObj.mjOBJ_JOINT, mujoco_name)
            if joint_id >= 0:
                angles[joint_name] = np.degrees(self.data.qpos[joint_id])
        return angles
    
    def get_end_effector_position(self) -> Tuple[float, float, float]:
        """取得末端執行器位置"""
        palm_id = mujoco.mj_name2id(self.model, mujoco.mjtObj.mjOBJ_BODY, "palm")
        if palm_id >= 0:
            return tuple(self.data.xpos[palm_id])
        return (0.0, 0.0, 0.0)
    
    def reset_simulation(self):
        """重置模擬"""
        mujoco.mj_resetData(self.model, self.data)
        logger.info("🔄 MuJoCo 模擬已重置")
    
    def start_viewer(self):
        """啟動 3D 檢視器"""
        if self.viewer is None:
            self.viewer = mujoco.viewer.launch_passive(self.model, self.data)
            logger.info("🎮 MuJoCo 3D 檢視器已啟動")
    
    def close_viewer(self):
        """關閉 3D 檢視器"""
        if self.viewer is not None:
            self.viewer.close()
            self.viewer = None
            logger.info("🛑 MuJoCo 3D 檢視器已關閉")
    
    async def run_simulation(self, duration: float = None):
        """運行物理模擬"""
        self.running = True
        start_time = time.time()
        
        logger.info("🚀 MuJoCo 物理模擬開始運行")
        
        while self.running:
            if duration and (time.time() - start_time) > duration:
                break
            
            # 執行物理步驟
            mujoco.mj_step(self.model, self.data)
            
            # 更新檢視器
            if self.viewer is not None:
                self.viewer.sync()
            
            # 控制模擬頻率
            await asyncio.sleep(self.timestep)
        
        logger.info("🛑 MuJoCo 物理模擬已停止")
    
    def stop_simulation(self):
        """停止模擬"""
        self.running = False

# 全域模擬器實例
_mujoco_simulator = None

def get_mujoco_simulator() -> InMoovMuJoCoSimulator:
    """取得 MuJoCo 模擬器實例"""
    global _mujoco_simulator
    if _mujoco_simulator is None:
        _mujoco_simulator = InMoovMuJoCoSimulator()
    return _mujoco_simulator

if __name__ == "__main__":
    # 測試 MuJoCo 模擬器
    async def test_mujoco():
        print("🎮 InMoov MuJoCo 3D 模擬器測試")
        print("=" * 50)
        
        simulator = get_mujoco_simulator()
        simulator.start_viewer()
        
        # 啟動物理模擬（背景運行）
        sim_task = asyncio.create_task(simulator.run_simulation())
        
        # 測試動作序列
        test_moves = [
            ("shoulder", 30, 2.0),
            ("elbow", -45, 2.0),
            ("wrist", 20, 1.5),
            ("thumb", 45, 1.0),
            ("index", 60, 1.0)
        ]
        
        for joint, angle, duration in test_moves:
            print(f"🎯 移動 {joint} 到 {angle}°")
            await simulator.move_joint(joint, angle, duration)
            await asyncio.sleep(0.5)
        
        print("✅ 測試完成")
        
        # 運行 10 秒後停止
        await asyncio.sleep(10)
        simulator.stop_simulation()
        simulator.close_viewer()
        
        await sim_task
    
    # 執行測試
    asyncio.run(test_mujoco())
