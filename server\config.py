"""
MCP 智慧機械手臂控制系統 - 系統設定管理
配置管理模組，負責載入和管理系統設定參數
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

class SystemConfig(BaseSettings):
    """系統設定類別"""
    
    # Gemini LLM 設定
    gemini_api_key: str = os.getenv("GEMINI_API_KEY", "")
    gemini_model: str = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
    
    # 伺服器設定
    server_host: str = os.getenv("SERVER_HOST", "localhost")
    server_port: int = int(os.getenv("SERVER_PORT", "8000"))
    
    # 日誌設定
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    log_file: str = os.getenv("LOG_FILE", "logs/system.log")
    
    # MuJoCo 設定
    mujoco_gl: str = os.getenv("MUJOCO_GL", "egl")
    mujoco_physics_timestep: float = float(os.getenv("MUJOCO_PHYSICS_TIMESTEP", "0.001"))
    
    # 機械手臂設定
    arm_dof: int = int(os.getenv("ARM_DOF", "11"))
    arm_max_torque: float = float(os.getenv("ARM_MAX_TORQUE", "15.0"))
    arm_position_tolerance: float = float(os.getenv("ARM_POSITION_TOLERANCE", "0.01"))
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 全域設定實例
config = SystemConfig()

def validate_config() -> bool:
    """驗證設定是否完整"""
    if not config.gemini_api_key:
        print("❌ 錯誤：未設定 GEMINI_API_KEY")
        print("請在 .env 檔案中設定您的 Gemini API 金鑰")
        return False
    
    print("✅ 系統設定驗證通過")
    print(f"📡 LLM 模型：{config.gemini_model}")
    print(f"🌐 伺服器：{config.server_host}:{config.server_port}")
    print(f"🤖 機械手臂自由度：{config.arm_dof}")
    return True

def get_config() -> SystemConfig:
    """取得系統設定"""
    return config

if __name__ == "__main__":
    # 測試設定載入
    print("🔧 MCP 智慧機械手臂控制系統 - 設定測試")
    print("=" * 50)
    
    if validate_config():
        print("\n📋 完整設定資訊：")
        print(f"  Gemini API Key: {'已設定' if config.gemini_api_key else '未設定'}")
        print(f"  Gemini 模型: {config.gemini_model}")
        print(f"  伺服器位址: {config.server_host}:{config.server_port}")
        print(f"  日誌等級: {config.log_level}")
        print(f"  日誌檔案: {config.log_file}")
        print(f"  MuJoCo GL: {config.mujoco_gl}")
        print(f"  物理時間步: {config.mujoco_physics_timestep}")
        print(f"  手臂自由度: {config.arm_dof}")
        print(f"  最大扭矩: {config.arm_max_torque}")
        print(f"  位置容差: {config.arm_position_tolerance}")
    else:
        print("\n❌ 設定驗證失敗，請檢查 .env 檔案")
