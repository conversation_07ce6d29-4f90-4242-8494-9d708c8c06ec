"""
MCP 智慧機械手臂控制系統 - 快速啟動腳本
一鍵啟動系統展示，自動處理環境檢查和設定
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

def print_banner():
    """顯示啟動橫幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    MCP 智慧機械手臂控制系統                                    ║
║                        🚀 快速啟動助手 🚀                                      ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  這個腳本將幫助您快速設定和啟動系統展示                                          ║
║  自動檢查環境、安裝相依套件、設定 API 金鑰                                      ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """檢查 Python 版本"""
    print("🐍 檢查 Python 版本...")
    
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        print(f"   當前版本: {sys.version}")
        return False
    
    print(f"✅ Python 版本: {sys.version.split()[0]}")
    return True

def check_and_install_requirements():
    """檢查並安裝相依套件"""
    print("\n📦 檢查相依套件...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ 找不到 requirements.txt 檔案")
        return False
    
    try:
        # 檢查是否需要安裝套件
        result = subprocess.run([
            sys.executable, "-m", "pip", "check"
        ], capture_output=True, text=True)
        
        # 嘗試導入關鍵套件
        try:
            import fastapi
            import uvicorn
            import google.generativeai
            import mujoco
            import pygame
            print("✅ 所有相依套件已安裝")
            return True
        except ImportError as e:
            print(f"⚠️ 缺少套件: {e.name}")
            
        # 安裝相依套件
        print("📥 正在安裝相依套件...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 相依套件安裝完成")
            return True
        else:
            print(f"❌ 套件安裝失敗: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 套件檢查失敗: {e}")
        return False

def setup_environment():
    """設定環境變數"""
    print("\n🔧 設定環境變數...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env 檔案已存在")
        
        # 檢查是否設定了 API 金鑰
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if "GEMINI_API_KEY=your_gemini_api_key_here" in content or "GEMINI_API_KEY=" in content and "GEMINI_API_KEY=sk-" not in content:
                print("⚠️ 需要設定 Gemini API 金鑰")
                return setup_api_key()
        
        print("✅ 環境變數設定完成")
        return True
    
    elif env_example.exists():
        # 複製範例檔案
        print("📝 建立 .env 檔案...")
        with open(env_example, 'r', encoding='utf-8') as f:
            content = f.read()
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ .env 檔案已建立")
        return setup_api_key()
    
    else:
        print("❌ 找不到 .env.example 檔案")
        return False

def setup_api_key():
    """設定 API 金鑰"""
    print("\n🔑 設定 Gemini API 金鑰")
    print("💡 您需要一個 Google Gemini API 金鑰來使用 LLM 功能")
    print("📖 取得方式: https://makersuite.google.com/app/apikey")
    
    while True:
        api_key = input("\n請輸入您的 Gemini API 金鑰 (或按 Enter 跳過): ").strip()
        
        if not api_key:
            print("⚠️ 跳過 API 金鑰設定")
            print("💡 您可以稍後在 .env 檔案中手動設定 GEMINI_API_KEY")
            return True
        
        if len(api_key) < 20:
            print("❌ API 金鑰格式不正確，請重新輸入")
            continue
        
        # 更新 .env 檔案
        env_file = Path(".env")
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替換 API 金鑰
        content = content.replace(
            "GEMINI_API_KEY=your_gemini_api_key_here",
            f"GEMINI_API_KEY={api_key}"
        )
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ API 金鑰設定完成")
        return True

def show_quick_start_menu():
    """顯示快速啟動選單"""
    menu = """
🎯 快速啟動選項：

1. 🧠 LLM 智慧驗證 (推薦首次體驗)
   - 快速驗證 LLM 真實智慧
   - 無需額外設定
   
2. 🎨 完整智慧展示
   - LLM 創造性規劃展示
   - 需要 API 金鑰
   
3. 🎮 3D 物理模擬
   - InMoov 機械手臂 3D 展示
   - 開啟 MuJoCo 檢視器
   
4. 🌐 完整系統展示
   - 啟動伺服器和完整功能
   - 最完整的體驗
   
5. 🎊 互動式選單
   - 進入完整互動式展示選單
   
0. 🚪 退出

請選擇啟動選項 (0-5): """
    
    print(menu)

async def run_quick_demo(choice: int):
    """執行快速展示"""
    demos = {
        1: ("simple_llm_test.py", "LLM 智慧驗證"),
        2: ("demo_real_llm_planning.py", "完整智慧展示"),
        3: ("test_inmoov_egg_picking.py", "3D 物理模擬"),
        4: ("complete_system_demo.py", "完整系統展示"),
        5: ("final_demo.py", "互動式選單")
    }
    
    if choice not in demos:
        print("❌ 無效選擇")
        return
    
    script, name = demos[choice]
    
    print(f"\n🚀 啟動: {name}")
    print("=" * 60)
    
    try:
        if choice == 4:
            # 完整系統展示需要先啟動伺服器
            print("🌐 正在啟動伺服器...")
            server_process = subprocess.Popen([
                sys.executable, "-m", "server.main"
            ])
            
            # 等待伺服器啟動
            await asyncio.sleep(5)
            
            try:
                # 執行展示
                process = subprocess.run([sys.executable, script])
            finally:
                # 停止伺服器
                server_process.terminate()
                server_process.wait()
        else:
            # 直接執行展示
            process = subprocess.run([sys.executable, script])
        
        print(f"\n✅ {name} 完成")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ {name} 已中斷")
    except Exception as e:
        print(f"\n❌ {name} 執行失敗: {e}")

def main():
    """主函數"""
    print_banner()
    
    # 檢查 Python 版本
    if not check_python_version():
        return
    
    # 檢查並安裝相依套件
    if not check_and_install_requirements():
        print("\n❌ 環境設定失敗，請手動安裝相依套件")
        print("💡 執行: pip install -r requirements.txt")
        return
    
    # 設定環境變數
    if not setup_environment():
        print("\n❌ 環境變數設定失敗")
        return
    
    print("\n🎊 環境設定完成！")
    
    # 顯示快速啟動選單
    while True:
        show_quick_start_menu()
        
        try:
            choice = input().strip()
            
            if choice == '0':
                print("👋 感謝使用，再見！")
                break
            
            elif choice in ['1', '2', '3', '4', '5']:
                asyncio.run(run_quick_demo(int(choice)))
                
                print("\n⏸️ 按 Enter 繼續...")
                input()
                
            else:
                print("❌ 無效選擇，請輸入 0-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 再見！")
            break
        except Exception as e:
            print(f"❌ 輸入處理錯誤: {e}")

if __name__ == "__main__":
    main()
