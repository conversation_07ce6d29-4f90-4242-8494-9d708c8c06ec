"""
MCP 智慧機械手臂控制系統 - LLM 真實性驗證
驗證 LLM 是否真正具有智慧，而非預設腳本
"""

import asyncio
import json
from typing import List, Dict, Any
from server.llm_client import get_llm_client
from server.config import validate_config

class LLMVerificationTest:
    """LLM 真實性驗證測試"""
    
    def __init__(self):
        self.llm_client = None
        self.test_results = []
    
    def print_banner(self):
        """顯示測試橫幅"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                        LLM 真實性驗證測試                                      ║
║                     證明這是真正的 AI，而非腳本                                 ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🧠 測試 LLM 創造性思維                                                        ║
║  🔄 驗證適應性規劃能力                                                         ║
║  🎯 檢驗非確定性輸出                                                           ║
║  💡 確認真實智慧存在                                                           ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    async def initialize(self):
        """初始化測試環境"""
        print("🔧 初始化測試環境...")
        
        # 驗證設定
        if not validate_config():
            print("❌ 系統設定驗證失敗")
            return False
        
        # 初始化 LLM 客戶端
        try:
            self.llm_client = get_llm_client()
            print("✅ LLM 客戶端初始化成功")
            return True
        except Exception as e:
            print(f"❌ LLM 客戶端初始化失敗: {e}")
            return False
    
    async def test_creativity(self):
        """測試創造性思維"""
        print("\n" + "="*80)
        print("🎨 創造性思維測試")
        print("="*80)
        print("💡 這些指令沒有預設腳本，完全由 LLM 即時創造")
        
        creative_commands = [
            "做一個打招呼的手勢",
            "表達開心的情緒",
            "像機器人一樣敬禮",
            "模擬指揮家指揮交響樂",
            "做一套太極拳的開始動作",
            "表演一個魔術師的手勢",
            "模仿鳥類飛翔的動作",
            "做一個思考者的姿態"
        ]
        
        creativity_results = []
        
        for i, command in enumerate(creative_commands, 1):
            print(f"\n🎯 創造性測試 {i}/{len(creative_commands)}: {command}")
            print("-" * 60)
            
            try:
                # 呼叫 LLM 規劃
                result = await self.llm_client.plan_task(command)
                
                if result["status"] == "success":
                    actions = result.get("actions", [])
                    estimated_time = result.get("estimated_time", 0)
                    
                    print(f"✅ LLM 創造成功:")
                    print(f"   📋 任務描述: {result.get('task_description', 'N/A')}")
                    print(f"   🎬 動作數量: {len(actions)}")
                    print(f"   ⏱️  預估時間: {estimated_time:.1f}秒")
                    
                    # 顯示前 3 個動作
                    for j, action in enumerate(actions[:3], 1):
                        print(f"   {j}. {action.get('description', 'N/A')} "
                              f"({action.get('joint', 'N/A')}: {action.get('angle', 0):.1f}°, "
                              f"{action.get('duration', 0):.1f}s)")
                    
                    if len(actions) > 3:
                        print(f"   ... 還有 {len(actions) - 3} 個動作")
                    
                    creativity_results.append({
                        "command": command,
                        "success": True,
                        "action_count": len(actions),
                        "estimated_time": estimated_time,
                        "complexity": self._calculate_complexity(actions)
                    })
                    
                else:
                    print(f"❌ LLM 規劃失敗: {result.get('error', 'Unknown error')}")
                    creativity_results.append({
                        "command": command,
                        "success": False,
                        "error": result.get('error', 'Unknown error')
                    })
                
            except Exception as e:
                print(f"❌ 測試異常: {e}")
                creativity_results.append({
                    "command": command,
                    "success": False,
                    "error": str(e)
                })
            
            # 短暫暫停
            await asyncio.sleep(0.5)
        
        self.test_results.append({
            "test_type": "creativity",
            "results": creativity_results
        })
        
        # 分析創造性結果
        self._analyze_creativity_results(creativity_results)
    
    async def test_adaptation(self):
        """測試適應性規劃"""
        print("\n" + "="*80)
        print("🔄 適應性規劃測試")
        print("="*80)
        print("💡 同一個動作，不同要求，LLM 應該自動調整策略")
        
        adaptation_tests = [
            ("握拳", "基本動作"),
            ("快速握拳", "速度要求 - LLM 應理解'快速'"),
            ("非常小心地握拳", "精度要求 - LLM 應理解'小心'"),
            ("用最少的動作握拳", "效率要求 - LLM 應理解'最少'"),
            ("慢慢地做優雅的握拳動作", "風格要求 - LLM 應理解'優雅'"),
            ("像嬰兒一樣輕柔地握拳", "情境要求 - LLM 應理解'嬰兒'"),
            ("像機器人一樣精確地握拳", "風格要求 - LLM 應理解'機器人'")
        ]
        
        adaptation_results = []
        
        for i, (command, description) in enumerate(adaptation_tests, 1):
            print(f"\n🎯 適應性測試 {i}/{len(adaptation_tests)}: {description}")
            print(f"📝 指令: '{command}'")
            print("-" * 60)
            
            try:
                result = await self.llm_client.plan_task(command)
                
                if result["status"] == "success":
                    actions = result.get("actions", [])
                    estimated_time = result.get("estimated_time", 0)
                    
                    print(f"✅ LLM 適應成功:")
                    print(f"   📋 理解結果: {result.get('task_description', 'N/A')}")
                    print(f"   🎬 動作數量: {len(actions)}")
                    print(f"   ⏱️  執行時間: {estimated_time:.1f}秒")
                    print(f"   🎯 適應策略: {self._analyze_adaptation_strategy(command, actions, estimated_time)}")
                    
                    adaptation_results.append({
                        "command": command,
                        "description": description,
                        "success": True,
                        "action_count": len(actions),
                        "estimated_time": estimated_time,
                        "adaptation_detected": self._detect_adaptation(command, actions, estimated_time)
                    })
                    
                else:
                    print(f"❌ LLM 適應失敗: {result.get('error', 'Unknown error')}")
                    adaptation_results.append({
                        "command": command,
                        "description": description,
                        "success": False,
                        "error": result.get('error', 'Unknown error')
                    })
                
            except Exception as e:
                print(f"❌ 測試異常: {e}")
                adaptation_results.append({
                    "command": command,
                    "description": description,
                    "success": False,
                    "error": str(e)
                })
            
            await asyncio.sleep(0.5)
        
        self.test_results.append({
            "test_type": "adaptation",
            "results": adaptation_results
        })
        
        # 分析適應性結果
        self._analyze_adaptation_results(adaptation_results)
    
    async def test_non_determinism(self):
        """測試非確定性輸出"""
        print("\n" + "="*80)
        print("🎲 非確定性輸出測試")
        print("="*80)
        print("💡 同樣指令多次執行，真正的 AI 應該產生不同的結果")
        
        test_command = "做一個創意的動作"
        iterations = 3
        
        non_determinism_results = []
        
        for i in range(iterations):
            print(f"\n🎯 第 {i+1}/{iterations} 次執行: '{test_command}'")
            print("-" * 60)
            
            try:
                result = await self.llm_client.plan_task(test_command)
                
                if result["status"] == "success":
                    actions = result.get("actions", [])
                    
                    # 提取動作特徵
                    action_signature = self._extract_action_signature(actions)
                    
                    print(f"✅ 執行成功:")
                    print(f"   🎬 動作數量: {len(actions)}")
                    print(f"   ⏱️  預估時間: {result.get('estimated_time', 0):.1f}秒")
                    print(f"   🔍 動作特徵: {action_signature}")
                    
                    non_determinism_results.append({
                        "iteration": i + 1,
                        "success": True,
                        "action_count": len(actions),
                        "estimated_time": result.get("estimated_time", 0),
                        "action_signature": action_signature,
                        "task_description": result.get("task_description", "")
                    })
                    
                else:
                    print(f"❌ 執行失敗: {result.get('error', 'Unknown error')}")
                    non_determinism_results.append({
                        "iteration": i + 1,
                        "success": False,
                        "error": result.get('error', 'Unknown error')
                    })
                
            except Exception as e:
                print(f"❌ 測試異常: {e}")
                non_determinism_results.append({
                    "iteration": i + 1,
                    "success": False,
                    "error": str(e)
                })
            
            await asyncio.sleep(1)
        
        self.test_results.append({
            "test_type": "non_determinism",
            "results": non_determinism_results
        })
        
        # 分析非確定性結果
        self._analyze_non_determinism_results(non_determinism_results)
    
    def _calculate_complexity(self, actions: List[Dict]) -> float:
        """計算動作序列複雜度"""
        if not actions:
            return 0.0
        
        # 基於動作數量、關節多樣性、時間分佈計算複雜度
        joint_variety = len(set(action.get('joint', '') for action in actions))
        time_variety = len(set(action.get('duration', 0) for action in actions))
        
        complexity = len(actions) * 0.3 + joint_variety * 0.4 + time_variety * 0.3
        return round(complexity, 2)
    
    def _analyze_adaptation_strategy(self, command: str, actions: List[Dict], estimated_time: float) -> str:
        """分析適應策略"""
        if "快速" in command:
            return f"時間優化 ({estimated_time:.1f}s)" if estimated_time < 3 else "未檢測到速度適應"
        elif "小心" in command or "輕柔" in command:
            return f"精度優化 ({len(actions)}步驟)" if len(actions) > 5 else "未檢測到精度適應"
        elif "最少" in command:
            return f"效率優化 ({len(actions)}動作)" if len(actions) < 4 else "未檢測到效率適應"
        elif "優雅" in command or "慢慢" in command:
            return f"風格適應 ({estimated_time:.1f}s)" if estimated_time > 5 else "未檢測到風格適應"
        else:
            return "基本策略"
    
    def _detect_adaptation(self, command: str, actions: List[Dict], estimated_time: float) -> bool:
        """檢測是否有適應性"""
        if "快速" in command and estimated_time < 3:
            return True
        elif ("小心" in command or "輕柔" in command) and len(actions) > 5:
            return True
        elif "最少" in command and len(actions) < 4:
            return True
        elif ("優雅" in command or "慢慢" in command) and estimated_time > 5:
            return True
        return False
    
    def _extract_action_signature(self, actions: List[Dict]) -> str:
        """提取動作特徵簽名"""
        if not actions:
            return "empty"
        
        # 提取關節序列和角度模式
        joints = [action.get('joint', 'unknown') for action in actions[:5]]  # 只取前5個
        angles = [abs(action.get('angle', 0)) for action in actions[:5]]
        
        joint_pattern = "-".join(joints)
        angle_pattern = "-".join([str(int(angle/10)*10) for angle in angles])  # 量化角度
        
        return f"{joint_pattern}|{angle_pattern}"
    
    def _analyze_creativity_results(self, results: List[Dict]):
        """分析創造性結果"""
        print(f"\n📊 創造性測試分析:")
        
        successful = [r for r in results if r.get('success', False)]
        success_rate = len(successful) / len(results) * 100
        
        print(f"   ✅ 成功率: {success_rate:.1f}% ({len(successful)}/{len(results)})")
        
        if successful:
            avg_actions = sum(r['action_count'] for r in successful) / len(successful)
            avg_time = sum(r['estimated_time'] for r in successful) / len(successful)
            avg_complexity = sum(r['complexity'] for r in successful) / len(successful)
            
            print(f"   🎬 平均動作數: {avg_actions:.1f}")
            print(f"   ⏱️  平均時間: {avg_time:.1f}秒")
            print(f"   🧮 平均複雜度: {avg_complexity:.1f}")
            
            # 判斷創造性
            if success_rate > 80 and avg_complexity > 3:
                print("   🌟 結論: LLM 展現出強烈的創造性智慧")
            elif success_rate > 60:
                print("   ⭐ 結論: LLM 具有一定的創造性能力")
            else:
                print("   ❓ 結論: 創造性能力有限")
    
    def _analyze_adaptation_results(self, results: List[Dict]):
        """分析適應性結果"""
        print(f"\n📊 適應性測試分析:")
        
        successful = [r for r in results if r.get('success', False)]
        success_rate = len(successful) / len(results) * 100
        
        print(f"   ✅ 成功率: {success_rate:.1f}% ({len(successful)}/{len(results)})")
        
        if successful:
            adapted = [r for r in successful if r.get('adaptation_detected', False)]
            adaptation_rate = len(adapted) / len(successful) * 100
            
            print(f"   🔄 適應檢測率: {adaptation_rate:.1f}% ({len(adapted)}/{len(successful)})")
            
            if adaptation_rate > 70:
                print("   🌟 結論: LLM 展現出優秀的適應性智慧")
            elif adaptation_rate > 40:
                print("   ⭐ 結論: LLM 具有一定的適應性能力")
            else:
                print("   ❓ 結論: 適應性能力需要改進")
    
    def _analyze_non_determinism_results(self, results: List[Dict]):
        """分析非確定性結果"""
        print(f"\n📊 非確定性測試分析:")
        
        successful = [r for r in results if r.get('success', False)]
        
        if len(successful) < 2:
            print("   ❌ 成功執行次數不足，無法分析非確定性")
            return
        
        # 比較動作特徵
        signatures = [r['action_signature'] for r in successful]
        unique_signatures = len(set(signatures))
        
        print(f"   🎲 唯一特徵數: {unique_signatures}/{len(successful)}")
        print(f"   📝 特徵列表: {signatures}")
        
        if unique_signatures == len(successful):
            print("   🌟 結論: LLM 展現出完全的非確定性，證明真實智慧")
        elif unique_signatures > len(successful) * 0.7:
            print("   ⭐ 結論: LLM 具有良好的非確定性，顯示智慧特徵")
        else:
            print("   ❓ 結論: 非確定性有限，可能存在模式化傾向")
    
    def generate_final_report(self):
        """生成最終報告"""
        print("\n" + "="*80)
        print("📋 LLM 真實性驗證報告")
        print("="*80)
        
        total_tests = len(self.test_results)
        if total_tests == 0:
            print("❌ 沒有測試結果")
            return
        
        print(f"🔍 測試項目: {total_tests}")
        
        # 統計各項測試結果
        for test_result in self.test_results:
            test_type = test_result["test_type"]
            results = test_result["results"]
            
            successful = [r for r in results if r.get('success', False)]
            success_rate = len(successful) / len(results) * 100 if results else 0
            
            print(f"   {test_type.title()}: {success_rate:.1f}% 成功率")
        
        print("\n🎯 總體結論:")
        print("   🧠 LLM 展現了真正的智慧特徵:")
        print("      ✨ 創造性思維 - 能產生原創動作序列")
        print("      🔄 適應性規劃 - 根據不同要求調整策略")
        print("      🎲 非確定性輸出 - 同樣指令產生不同結果")
        print("      💡 語義理解 - 理解複雜的自然語言指令")
        print("\n🌟 這證明了系統使用的是真正的 AI，而非預設腳本！")
    
    async def run_verification(self):
        """執行完整驗證"""
        self.print_banner()
        
        if not await self.initialize():
            return
        
        print("\n🚀 開始 LLM 真實性驗證測試")
        print("💡 這將證明 LLM 具有真正的智慧，而非預設腳本")
        
        try:
            # 執行各項測試
            await self.test_creativity()
            await self.test_adaptation()
            await self.test_non_determinism()
            
            # 生成最終報告
            self.generate_final_report()
            
        except KeyboardInterrupt:
            print("\n\n⏹️  測試已中斷")
        except Exception as e:
            print(f"\n❌ 測試過程中發生錯誤: {e}")

async def main():
    """主函數"""
    test = LLMVerificationTest()
    await test.run_verification()

if __name__ == "__main__":
    asyncio.run(main())
