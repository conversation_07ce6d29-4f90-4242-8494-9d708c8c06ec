"""
MCP 智慧機械手臂控制系統 - FastAPI 主伺服器
提供 RESTful API 接口，整合 LLM 規劃和機械手臂控制功能
"""

import asyncio
import uvicorn
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from loguru import logger

from .config import get_config, validate_config
from .llm_client import get_llm_client
from .task_planner import get_task_planner, TaskStatus
from .arm_simulator import get_simulator
from .inmoov_mujoco_simulator import get_mujoco_simulator
from .arm_driver import get_hardware_driver

# 初始化設定
config = get_config()

# 建立 FastAPI 應用
app = FastAPI(
    title="MCP 智慧機械手臂控制系統",
    description="基於 LLM 的自主任務規劃與 MuJoCo 3D 物理模擬平台",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 設定 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 請求模型
class TaskRequest(BaseModel):
    command: str
    context: Optional[Dict[str, Any]] = None
    simulator_type: Optional[str] = "2d"  # "2d", "3d", "hardware"

class JointMoveRequest(BaseModel):
    joint: str
    angle: float
    duration: float = 1.0

# 回應模型
class TaskResponse(BaseModel):
    task_id: str
    status: str
    message: str
    plan: Optional[Dict[str, Any]] = None

class SystemStatusResponse(BaseModel):
    system_status: str
    llm_model: str
    mujoco_version: str
    active_tasks: int
    joint_positions: Dict[str, float]
    physics_stable: bool
    timestamp: str

# 全域變數
task_planner = None
llm_client = None
simulator_2d = None
simulator_3d = None
hardware_driver = None

@app.on_event("startup")
async def startup_event():
    """應用啟動事件"""
    global task_planner, llm_client, simulator_2d, simulator_3d, hardware_driver
    
    logger.info("🚀 MCP 智慧機械手臂控制系統啟動中...")
    
    # 驗證設定
    if not validate_config():
        logger.error("❌ 系統設定驗證失敗")
        raise RuntimeError("系統設定驗證失敗")
    
    # 初始化核心元件
    try:
        llm_client = get_llm_client()
        task_planner = get_task_planner()
        simulator_2d = get_simulator()
        simulator_3d = get_mujoco_simulator()
        hardware_driver = get_hardware_driver()
        
        logger.info("✅ 所有核心元件初始化完成")
        logger.info(f"🌐 伺服器啟動於: http://{config.server_host}:{config.server_port}")
        
    except Exception as e:
        logger.error(f"❌ 核心元件初始化失敗: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """應用關閉事件"""
    logger.info("🛑 MCP 智慧機械手臂控制系統關閉中...")
    
    # 清理資源
    if simulator_3d:
        simulator_3d.close_viewer()
    
    if hardware_driver:
        await hardware_driver.disconnect()
    
    logger.info("✅ 系統已安全關閉")

@app.get("/")
async def root():
    """根路徑"""
    return {
        "message": "MCP 智慧機械手臂控制系統",
        "description": "基於 LLM 的自主任務規劃與 MuJoCo 3D 物理模擬平台",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.post("/api/task", response_model=TaskResponse)
async def create_task(request: TaskRequest, background_tasks: BackgroundTasks):
    """建立並執行新任務"""
    try:
        logger.info(f"📝 收到新任務: {request.command}")
        
        # 建立任務
        task = await task_planner.create_task(request.command, request.context)
        
        # 在背景執行任務
        background_tasks.add_task(execute_task_background, task.id, request.simulator_type)
        
        return TaskResponse(
            task_id=task.id,
            status="created",
            message="任務已建立，正在規劃中..."
        )
        
    except Exception as e:
        logger.error(f"❌ 建立任務失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def execute_task_background(task_id: str, simulator_type: str):
    """背景執行任務"""
    try:
        # 規劃任務
        plan_result = await task_planner.plan_task(task_id)
        
        if plan_result["status"] != "success":
            logger.error(f"❌ 任務規劃失敗: {task_id}")
            return
        
        # 選擇模擬器
        simulator = None
        if simulator_type == "2d":
            simulator = simulator_2d
        elif simulator_type == "3d":
            simulator = simulator_3d
        elif simulator_type == "hardware":
            simulator = hardware_driver
        
        # 執行任務
        exec_result = await task_planner.execute_task(task_id, simulator)
        
        if exec_result["status"] == "success":
            logger.info(f"✅ 任務執行完成: {task_id}")
        else:
            logger.error(f"❌ 任務執行失敗: {task_id}")
            
    except Exception as e:
        logger.error(f"❌ 背景任務執行異常: {e}")

@app.get("/api/task/{task_id}")
async def get_task(task_id: str):
    """取得任務資訊"""
    task = task_planner.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任務不存在")
    
    return task.to_dict()

@app.get("/api/tasks")
async def get_tasks():
    """取得所有任務"""
    active_tasks = task_planner.get_active_tasks()
    task_history = task_planner.get_task_history()
    
    return {
        "active_tasks": active_tasks,
        "task_history": task_history[-10:]  # 只返回最近 10 個歷史任務
    }

@app.delete("/api/task/{task_id}")
async def cancel_task(task_id: str):
    """取消任務"""
    success = task_planner.cancel_task(task_id)
    if not success:
        raise HTTPException(status_code=404, detail="任務不存在")
    
    return {"message": "任務已取消"}

@app.post("/api/joint/move")
async def move_joint(request: JointMoveRequest):
    """直接控制關節移動"""
    try:
        # 預設使用 2D 模擬器
        await simulator_2d.move_joint(request.joint, request.angle, request.duration)
        
        return {
            "status": "success",
            "message": f"關節 {request.joint} 已移動到 {request.angle}°"
        }
        
    except Exception as e:
        logger.error(f"❌ 關節移動失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/joints")
async def get_joint_angles():
    """取得所有關節角度"""
    try:
        angles = simulator_2d.get_joint_angles()
        return {
            "joint_angles": angles,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ 取得關節角度失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/status", response_model=SystemStatusResponse)
async def get_system_status():
    """取得系統狀態"""
    try:
        joint_angles = simulator_2d.get_joint_angles()
        active_tasks = len(task_planner.get_active_tasks())
        
        return SystemStatusResponse(
            system_status="running",
            llm_model=config.gemini_model,
            mujoco_version="3.3.5",
            active_tasks=active_tasks,
            joint_positions=joint_angles,
            physics_stable=True,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"❌ 取得系統狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/emergency_stop")
async def emergency_stop():
    """緊急停止"""
    try:
        # 停止所有活躍任務
        active_tasks = task_planner.get_active_tasks()
        for task in active_tasks:
            task_planner.cancel_task(task["id"])
        
        # 停止硬體（如果連接）
        if hardware_driver and hardware_driver.connected:
            await hardware_driver.emergency_stop()
        
        logger.warning("🚨 緊急停止已執行")
        
        return {
            "status": "success",
            "message": "緊急停止已執行",
            "cancelled_tasks": len(active_tasks)
        }
        
    except Exception as e:
        logger.error(f"❌ 緊急停止失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/reset")
async def reset_system():
    """重置系統"""
    try:
        # 取消所有任務
        active_tasks = task_planner.get_active_tasks()
        for task in active_tasks:
            task_planner.cancel_task(task["id"])
        
        # 重置模擬器
        simulator_2d.reset_arm()
        simulator_3d.reset_simulation()
        
        logger.info("🔄 系統已重置")
        
        return {
            "status": "success",
            "message": "系統已重置"
        }
        
    except Exception as e:
        logger.error(f"❌ 系統重置失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/health")
async def health_check():
    """健康檢查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "llm_client": "ok" if llm_client else "error",
            "task_planner": "ok" if task_planner else "error",
            "simulator_2d": "ok" if simulator_2d else "error",
            "simulator_3d": "ok" if simulator_3d else "error",
            "hardware_driver": "ok" if hardware_driver else "error"
        }
    }

if __name__ == "__main__":
    # 設定日誌
    logger.add(
        config.log_file,
        rotation="1 day",
        retention="7 days",
        level=config.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    )
    
    # 啟動伺服器
    uvicorn.run(
        "server.main:app",
        host=config.server_host,
        port=config.server_port,
        reload=True,
        log_level=config.log_level.lower()
    )
