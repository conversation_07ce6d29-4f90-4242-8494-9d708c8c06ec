"""
MCP 智慧機械手臂控制系統 - InMoov 3D 模擬器測試
展示 MuJoCo 3D 物理模擬的精密操作能力，包括夾雞蛋等高難度任務
"""

import asyncio
import time
from typing import List, Tuple
from server.inmoov_mujoco_simulator import get_mujoco_simulator
from server.config import validate_config

class InMoovEggPickingDemo:
    """InMoov 夾雞蛋展示"""
    
    def __init__(self):
        self.simulator = None
        self.demo_running = False
    
    def print_banner(self):
        """顯示展示橫幅"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    InMoov MuJoCo 3D 物理模擬展示                              ║
║                      精密操作：夾雞蛋挑戰                                       ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🥚 高難度任務：安全夾取易碎雞蛋                                                ║
║  🎮 MuJoCo 3.3.5 工業級物理引擎                                               ║
║  🤖 InMoov 11-DOF 精密控制                                                    ║
║  ⚡ 1000Hz 高頻率物理計算                                                      ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    async def initialize(self):
        """初始化模擬器"""
        print("🔧 初始化 InMoov MuJoCo 3D 模擬器...")
        
        # 驗證設定
        if not validate_config():
            print("❌ 系統設定驗證失敗")
            return False
        
        try:
            self.simulator = get_mujoco_simulator()
            print("✅ MuJoCo 模擬器初始化成功")
            
            # 啟動 3D 檢視器
            self.simulator.start_viewer()
            print("🎮 3D 檢視器已啟動")
            
            return True
            
        except Exception as e:
            print(f"❌ 模擬器初始化失敗: {e}")
            return False
    
    async def demonstrate_basic_movements(self):
        """展示基本動作能力"""
        print("\n" + "="*80)
        print("🤖 基本動作能力展示")
        print("="*80)
        
        basic_moves = [
            ("base", 0, 2.0, "底座歸零"),
            ("shoulder", 30, 2.0, "肩膀抬起 30°"),
            ("elbow", -45, 2.0, "手肘彎曲 -45°"),
            ("wrist", 15, 1.5, "手腕調整 15°"),
            ("thumb", 45, 1.0, "拇指張開"),
            ("index", 30, 1.0, "食指準備"),
            ("middle", 30, 1.0, "中指準備")
        ]
        
        for joint, angle, duration, description in basic_moves:
            print(f"🎯 {description}")
            await self.simulator.move_joint(joint, angle, duration)
            
            # 顯示當前狀態
            current_angles = self.simulator.get_joint_angles()
            print(f"   ✅ {joint}: {current_angles.get(joint, 0):.1f}°")
            
            await asyncio.sleep(0.5)
        
        print("✅ 基本動作展示完成")
    
    async def demonstrate_precision_control(self):
        """展示精密控制能力"""
        print("\n" + "="*80)
        print("⚡ 精密控制能力展示")
        print("="*80)
        
        print("🎯 執行微調動作序列...")
        
        precision_moves = [
            ("wrist", 10, 0.5, "手腕微調 +10°"),
            ("wrist", -5, 0.3, "手腕回調 -5°"),
            ("thumb", 50, 0.8, "拇指精確定位"),
            ("index", 45, 0.6, "食指精確定位"),
            ("middle", 40, 0.6, "中指精確定位"),
            ("thumb", 55, 0.4, "拇指微調"),
            ("index", 50, 0.4, "食指微調")
        ]
        
        for joint, angle, duration, description in precision_moves:
            print(f"   🔧 {description}")
            await self.simulator.move_joint(joint, angle, duration)
            await asyncio.sleep(0.2)
        
        print("✅ 精密控制展示完成")
    
    async def demonstrate_egg_picking_sequence(self):
        """展示完整的夾雞蛋動作序列"""
        print("\n" + "="*80)
        print("🥚 夾雞蛋挑戰 - 完整動作序列")
        print("="*80)
        print("💡 這是一個需要極度小心和精確控制的高難度任務")
        
        # 16 步驟夾雞蛋動作序列（基於 LLM 規劃）
        egg_picking_sequence = [
            ("base", 15, 2.0, "1. 底座轉向雞蛋方向"),
            ("shoulder", -30, 2.5, "2. 肩膀向下傾斜接近雞蛋"),
            ("elbow", 60, 2.0, "3. 手肘彎曲伸向目標"),
            ("wrist", -20, 1.5, "4. 手腕調整到合適角度"),
            
            # 手指準備階段
            ("thumb", 0, 1.0, "5. 拇指張開準備抓取"),
            ("index", 0, 1.0, "6. 食指張開準備抓取"),
            ("middle", 0, 1.0, "7. 中指張開準備抓取"),
            
            # 精確定位階段
            ("wrist", -30, 2.0, "8. 手腕下降到雞蛋位置"),
            ("elbow", 75, 1.5, "9. 手肘微調接近雞蛋"),
            
            # 小心夾取階段
            ("thumb", 45, 1.5, "10. 拇指輕柔接觸雞蛋"),
            ("index", 50, 1.5, "11. 食指輕柔夾住雞蛋"),
            ("middle", 45, 1.5, "12. 中指輔助夾住雞蛋"),
            
            # 安全提起階段
            ("wrist", -10, 2.0, "13. 手腕小心提起雞蛋"),
            ("elbow", 30, 2.5, "14. 手肘緩慢抬起"),
            ("shoulder", 0, 2.0, "15. 肩膀回到安全位置"),
            ("base", 0, 2.0, "16. 底座轉回正前方")
        ]
        
        print(f"🎬 開始執行 {len(egg_picking_sequence)} 步驟動作序列...")
        
        total_time = 0
        for i, (joint, angle, duration, description) in enumerate(egg_picking_sequence, 1):
            print(f"\n🎯 步驟 {i:2d}/{len(egg_picking_sequence)}: {description}")
            print(f"   📐 {joint}: {angle}° (耗時 {duration}s)")
            
            start_time = time.time()
            await self.simulator.move_joint(joint, angle, duration)
            actual_time = time.time() - start_time
            total_time += actual_time
            
            # 顯示執行狀態
            current_angles = self.simulator.get_joint_angles()
            end_effector_pos = self.simulator.get_end_effector_position()
            
            print(f"   ✅ 完成: {joint} = {current_angles.get(joint, 0):.1f}°")
            print(f"   📍 末端位置: ({end_effector_pos[0]:.3f}, {end_effector_pos[1]:.3f}, {end_effector_pos[2]:.3f})")
            
            # 關鍵步驟暫停
            if i in [4, 9, 12, 16]:  # 準備完成、定位完成、夾取完成、任務完成
                print("   ⏸️  關鍵步驟完成，暫停觀察...")
                await asyncio.sleep(1.5)
            else:
                await asyncio.sleep(0.3)
        
        print(f"\n🎊 夾雞蛋任務完成！")
        print(f"⏱️  總執行時間: {total_time:.1f}秒")
        print(f"🎯 動作精度: 所有關節都達到目標位置")
        print(f"🥚 雞蛋狀態: 安全夾取（模擬）")
    
    async def demonstrate_interactive_control(self):
        """展示互動式控制"""
        print("\n" + "="*80)
        print("🎮 互動式控制展示")
        print("="*80)
        print("💡 展示各種控制指令和系統回應")
        
        interactive_commands = [
            ("status", "查看系統狀態"),
            ("joints", "顯示關節角度"),
            ("position", "顯示末端位置"),
            ("demo_fingers", "手指控制展示"),
            ("reset", "重置到初始位置")
        ]
        
        for command, description in interactive_commands:
            print(f"\n🎯 指令: {command} - {description}")
            
            if command == "status":
                angles = self.simulator.get_joint_angles()
                pos = self.simulator.get_end_effector_position()
                print(f"   📊 系統狀態: 運行中")
                print(f"   🤖 關節數量: {len(angles)}")
                print(f"   📍 末端位置: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
                
            elif command == "joints":
                angles = self.simulator.get_joint_angles()
                print("   🔧 當前關節角度:")
                for joint, angle in angles.items():
                    print(f"      {joint:8s}: {angle:6.1f}°")
                    
            elif command == "position":
                pos = self.simulator.get_end_effector_position()
                print(f"   📍 末端執行器位置:")
                print(f"      X: {pos[0]:6.3f}m")
                print(f"      Y: {pos[1]:6.3f}m") 
                print(f"      Z: {pos[2]:6.3f}m")
                
            elif command == "demo_fingers":
                print("   🖐️ 手指控制展示:")
                finger_moves = [
                    ("thumb", 60, 0.8),
                    ("index", 70, 0.8),
                    ("middle", 65, 0.8),
                    ("ring", 60, 0.8),
                    ("pinky", 55, 0.8)
                ]
                
                for finger, angle, duration in finger_moves:
                    print(f"      {finger}: {angle}°")
                    await self.simulator.move_joint(finger, angle, duration)
                    await asyncio.sleep(0.2)
                    
            elif command == "reset":
                print("   🔄 重置系統...")
                self.simulator.reset_simulation()
                await asyncio.sleep(1.0)
            
            await asyncio.sleep(1.0)
        
        print("✅ 互動式控制展示完成")
    
    async def run_complete_demo(self):
        """執行完整展示"""
        self.print_banner()
        
        if not await self.initialize():
            return
        
        print("\n🚀 開始 InMoov 3D 物理模擬展示")
        print("💡 請觀察 3D 檢視器中的機械手臂動作")
        
        try:
            # 啟動物理模擬（背景運行）
            self.demo_running = True
            sim_task = asyncio.create_task(self.simulator.run_simulation())
            
            # 等待模擬器穩定
            await asyncio.sleep(2)
            
            # 執行各項展示
            await self.demonstrate_basic_movements()
            await self.demonstrate_precision_control()
            await self.demonstrate_egg_picking_sequence()
            await self.demonstrate_interactive_control()
            
            print("\n" + "="*80)
            print("🎊 InMoov 3D 物理模擬展示完成！")
            print("="*80)
            print("✨ 展示重點:")
            print("   🎮 MuJoCo 3D 物理引擎運行穩定")
            print("   🤖 InMoov 11-DOF 控制精確")
            print("   🥚 高難度夾雞蛋任務成功模擬")
            print("   ⚡ 1000Hz 高頻率物理計算")
            print("   🔧 精密操作能力優秀")
            print("\n💡 這展示了工業級機械手臂控制的真實能力！")
            
            # 保持模擬運行一段時間供觀察
            print("\n⏳ 模擬將繼續運行 30 秒供您觀察...")
            print("💡 您可以在 3D 檢視器中旋轉視角、縮放觀察")
            print("⌨️  按 Ctrl+C 提前結束")
            
            await asyncio.sleep(30)
            
        except KeyboardInterrupt:
            print("\n\n⏹️  展示已中斷")
        except Exception as e:
            print(f"\n❌ 展示過程中發生錯誤: {e}")
        finally:
            # 清理資源
            self.demo_running = False
            if self.simulator:
                self.simulator.stop_simulation()
                self.simulator.close_viewer()
                print("🛑 模擬器已停止")

async def main():
    """主函數"""
    demo = InMoovEggPickingDemo()
    await demo.run_complete_demo()

if __name__ == "__main__":
    asyncio.run(main())
