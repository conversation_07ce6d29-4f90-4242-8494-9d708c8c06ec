"""
MCP 智慧機械手臂控制系統 - 最終展示腳本
整合所有功能的完整展示，展現系統的全部能力
"""

import asyncio
import subprocess
import sys
import time
from typing import Optional

class FinalSystemDemo:
    """最終系統展示"""
    
    def __init__(self):
        self.server_process: Optional[subprocess.Popen] = None
        self.demo_running = False
    
    def print_welcome_banner(self):
        """顯示歡迎橫幅"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    MCP 智慧機械手臂控制系統                                    ║
║                        🎊 最終完整展示 🎊                                      ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  🧠 Gemini 1.5 Flash LLM 智慧規劃                                            ║
║  🎮 MuJoCo 3.3.5 高精度 3D 物理模擬                                          ║
║  🤖 InMoov 11-DOF 機械手臂控制                                               ║
║  🌐 RESTful API 完整接口                                                      ║
║  ⚡ 1000Hz 高頻率物理計算                                                      ║
║                                                                              ║
║  這是一個革命性的機械手臂控制系統，結合了真正的 AI 智慧                          ║
║  與工業級物理模擬，實現自然語言到機械動作的完美轉換                              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def show_demo_menu(self):
        """顯示展示選單"""
        menu = """
🎯 展示選項：

1. 🧠 LLM 真實智慧驗證
   - 證明這是真正的 AI，而非預設腳本
   - 測試創造性、適應性和非確定性
   
2. 🎨 LLM 智慧規劃展示
   - 展示創造性任務規劃
   - 適應性策略調整
   - 上下文理解能力
   
3. 🎮 InMoov 3D 物理模擬
   - MuJoCo 高精度物理引擎
   - 夾雞蛋等精密操作展示
   - 11-DOF 完整控制
   
4. 🌐 完整系統整合展示
   - API 接口功能
   - 多模擬器協同
   - 端到端工作流程
   
5. 🚀 全功能綜合展示
   - 所有功能的完整展示
   - 最佳體驗路徑
   
0. 🚪 退出展示

請選擇展示項目 (0-5): """
        
        print(menu)
    
    def check_dependencies(self) -> bool:
        """檢查相依套件"""
        print("🔍 檢查系統相依套件...")
        
        required_packages = [
            "fastapi",
            "uvicorn", 
            "google-generativeai",
            "mujoco",
            "pygame",
            "numpy",
            "requests"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
                print(f"   ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"   ❌ {package}")
        
        if missing_packages:
            print(f"\n❌ 缺少相依套件: {', '.join(missing_packages)}")
            print("💡 請執行: pip install -r requirements.txt")
            return False
        
        print("✅ 所有相依套件檢查通過")
        return True
    
    def check_environment(self) -> bool:
        """檢查環境設定"""
        print("\n🔧 檢查環境設定...")
        
        import os
        from dotenv import load_dotenv
        
        load_dotenv()
        
        gemini_api_key = os.getenv("GEMINI_API_KEY")
        
        if not gemini_api_key:
            print("❌ 未設定 GEMINI_API_KEY")
            print("💡 請在 .env 檔案中設定您的 Gemini API 金鑰")
            print("📝 範例: GEMINI_API_KEY=your_api_key_here")
            return False
        
        print("✅ 環境設定檢查通過")
        return True
    
    async def start_server(self) -> bool:
        """啟動伺服器"""
        print("\n🚀 啟動 MCP 伺服器...")
        
        try:
            # 啟動 FastAPI 伺服器
            self.server_process = subprocess.Popen([
                sys.executable, "-m", "server.main"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待伺服器啟動
            print("⏳ 等待伺服器啟動...")
            await asyncio.sleep(5)
            
            # 檢查伺服器狀態
            import requests
            try:
                response = requests.get("http://localhost:8000/api/health", timeout=5)
                if response.status_code == 200:
                    print("✅ MCP 伺服器啟動成功")
                    return True
                else:
                    print(f"❌ 伺服器回應異常: {response.status_code}")
                    return False
            except requests.exceptions.RequestException:
                print("❌ 無法連接到伺服器")
                return False
                
        except Exception as e:
            print(f"❌ 伺服器啟動失敗: {e}")
            return False
    
    def stop_server(self):
        """停止伺服器"""
        if self.server_process:
            print("🛑 停止 MCP 伺服器...")
            self.server_process.terminate()
            self.server_process.wait()
            self.server_process = None
    
    async def run_demo(self, demo_choice: int):
        """執行指定的展示"""
        print(f"\n🎬 開始執行展示 {demo_choice}")
        print("=" * 80)
        
        try:
            if demo_choice == 1:
                # LLM 真實智慧驗證
                print("🧠 啟動 LLM 真實智慧驗證...")
                process = await asyncio.create_subprocess_exec(
                    sys.executable, "simple_llm_test.py",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                
                if stdout:
                    print(stdout.decode())
                if stderr:
                    print(f"錯誤輸出: {stderr.decode()}")
                    
            elif demo_choice == 2:
                # LLM 智慧規劃展示
                print("🎨 啟動 LLM 智慧規劃展示...")
                process = await asyncio.create_subprocess_exec(
                    sys.executable, "demo_real_llm_planning.py",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                
                if stdout:
                    print(stdout.decode())
                if stderr:
                    print(f"錯誤輸出: {stderr.decode()}")
                    
            elif demo_choice == 3:
                # InMoov 3D 物理模擬
                print("🎮 啟動 InMoov 3D 物理模擬展示...")
                print("💡 這將開啟 3D 檢視器視窗，請注意觀察")
                
                process = await asyncio.create_subprocess_exec(
                    sys.executable, "test_inmoov_egg_picking.py",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                
                if stdout:
                    print(stdout.decode())
                if stderr:
                    print(f"錯誤輸出: {stderr.decode()}")
                    
            elif demo_choice == 4:
                # 完整系統整合展示
                print("🌐 啟動完整系統整合展示...")
                process = await asyncio.create_subprocess_exec(
                    sys.executable, "complete_system_demo.py",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                
                if stdout:
                    print(stdout.decode())
                if stderr:
                    print(f"錯誤輸出: {stderr.decode()}")
                    
            elif demo_choice == 5:
                # 全功能綜合展示
                print("🚀 啟動全功能綜合展示...")
                print("💡 這將依序執行所有展示項目")
                
                demos = [
                    ("simple_llm_test.py", "LLM 真實智慧驗證"),
                    ("demo_real_llm_planning.py", "LLM 智慧規劃展示"),
                    ("complete_system_demo.py", "完整系統整合展示")
                ]
                
                for demo_script, demo_name in demos:
                    print(f"\n🎯 執行: {demo_name}")
                    print("-" * 60)
                    
                    process = await asyncio.create_subprocess_exec(
                        sys.executable, demo_script,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    stdout, stderr = await process.communicate()
                    
                    if stdout:
                        # 只顯示關鍵輸出
                        output_lines = stdout.decode().split('\n')
                        key_lines = [line for line in output_lines 
                                   if any(marker in line for marker in ['✅', '❌', '🎊', '🌟', '📊'])]
                        for line in key_lines[-10:]:  # 只顯示最後10行關鍵資訊
                            print(line)
                    
                    print(f"✅ {demo_name} 完成")
                    await asyncio.sleep(2)
                
                print("\n🎊 全功能綜合展示完成！")
                
        except Exception as e:
            print(f"❌ 展示執行失敗: {e}")
    
    def show_final_summary(self):
        """顯示最終總結"""
        summary = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                            🎊 展示完成總結 🎊                                   ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  🌟 您已體驗了 MCP 智慧機械手臂控制系統的完整功能：                              ║
║                                                                              ║
║  🧠 真正的 AI 智慧                                                            ║
║     • Gemini 1.5 Flash 展現創造性和適應性                                     ║
║     • 非預設腳本的真實智慧規劃                                                  ║
║     • 自然語言到動作的完美轉換                                                  ║
║                                                                              ║
║  🎮 工業級物理模擬                                                             ║
║     • MuJoCo 3.3.5 高精度計算                                                ║
║     • InMoov 11-DOF 精密控制                                                 ║
║     • 夾雞蛋等高難度任務                                                       ║
║                                                                              ║
║  🌐 完整系統整合                                                               ║
║     • RESTful API 接口                                                       ║
║     • 多模擬器協同工作                                                         ║
║     • 端到端工作流程                                                           ║
║                                                                              ║
║  💡 這就是未來機器人控制技術的完美展示！                                        ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

🚀 下一步建議：
   • 探索更多創意指令
   • 整合到實際機器人硬體
   • 開發自定義應用場景
   • 貢獻到開源社群

📞 技術支援：
   • 查看 README.md 獲取詳細文件
   • 使用 GitHub Issues 回報問題
   • 加入技術討論社群

感謝您體驗 MCP 智慧機械手臂控制系統！ 🤖✨
        """
        print(summary)
    
    async def run_interactive_demo(self):
        """執行互動式展示"""
        self.print_welcome_banner()
        
        # 檢查系統環境
        if not self.check_dependencies():
            return
        
        if not self.check_environment():
            return
        
        # 啟動伺服器
        server_started = await self.start_server()
        
        try:
            while True:
                self.show_demo_menu()
                
                try:
                    choice = input().strip()
                    
                    if choice == '0':
                        print("👋 感謝您的體驗，再見！")
                        break
                    
                    elif choice in ['1', '2', '3', '4', '5']:
                        demo_choice = int(choice)
                        
                        if demo_choice in [4, 5] and not server_started:
                            print("❌ 需要伺服器運行的展示無法執行")
                            print("💡 請檢查伺服器啟動狀態")
                            continue
                        
                        await self.run_demo(demo_choice)
                        
                        print("\n⏸️  展示完成，按 Enter 繼續...")
                        input()
                        
                    else:
                        print("❌ 無效選擇，請輸入 0-5")
                        
                except KeyboardInterrupt:
                    print("\n\n⏹️  展示已中斷")
                    break
                except Exception as e:
                    print(f"❌ 輸入處理錯誤: {e}")
        
        finally:
            # 清理資源
            self.stop_server()
            self.show_final_summary()

async def main():
    """主函數"""
    demo = FinalSystemDemo()
    await demo.run_interactive_demo()

if __name__ == "__main__":
    asyncio.run(main())
