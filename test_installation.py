"""
MCP 智慧機械手臂控制系統 - 安裝測試
快速驗證系統安裝是否正確
"""

import sys
import importlib
from pathlib import Path

def print_banner():
    """顯示測試橫幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    MCP 智慧機械手臂控制系統                                    ║
║                        🔍 安裝驗證測試 🔍                                      ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def test_python_version():
    """測試 Python 版本"""
    print("🐍 檢查 Python 版本...")
    
    version = sys.version_info
    if version >= (3, 8):
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} (符合要求)")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} (需要 3.8+)")
        return False

def test_required_packages():
    """測試必要套件"""
    print("\n📦 檢查必要套件...")
    
    required_packages = [
        ("fastapi", "FastAPI Web 框架"),
        ("uvicorn", "ASGI 伺服器"),
        ("pydantic", "資料驗證"),
        ("google.generativeai", "Google Gemini API"),
        ("mujoco", "MuJoCo 物理引擎"),
        ("pygame", "2D 圖形庫"),
        ("numpy", "數值計算"),
        ("requests", "HTTP 客戶端"),
        ("dotenv", "環境變數管理"),
        ("loguru", "日誌系統")
    ]
    
    success_count = 0
    
    for package_name, description in required_packages:
        try:
            importlib.import_module(package_name)
            print(f"✅ {package_name:20s} - {description}")
            success_count += 1
        except ImportError:
            print(f"❌ {package_name:20s} - {description} (未安裝)")
    
    print(f"\n📊 套件檢查結果: {success_count}/{len(required_packages)} 通過")
    return success_count == len(required_packages)

def test_project_structure():
    """測試專案結構"""
    print("\n📁 檢查專案結構...")
    
    required_files = [
        ("README.md", "專案說明文件"),
        ("requirements.txt", "相依套件清單"),
        (".env.example", "環境變數範例"),
        ("server/", "伺服器核心目錄"),
        ("server/__init__.py", "伺服器套件初始化"),
        ("server/main.py", "FastAPI 主伺服器"),
        ("server/config.py", "系統設定管理"),
        ("server/llm_client.py", "Gemini LLM 客戶端"),
        ("server/task_planner.py", "智慧任務規劃器"),
        ("server/arm_simulator.py", "2D Pygame 模擬器"),
        ("server/inmoov_mujoco_simulator.py", "InMoov 3D 物理模擬器"),
        ("server/arm_driver.py", "硬體驅動程式"),
        ("complete_system_demo.py", "完整系統展示"),
        ("demo_real_llm_planning.py", "LLM 智慧展示"),
        ("simple_llm_test.py", "LLM 真實性驗證"),
        ("test_inmoov_egg_picking.py", "InMoov 3D 模擬器測試"),
        ("final_demo.py", "最終展示腳本"),
        ("start_demo.py", "快速啟動腳本"),
        ("logs/", "日誌目錄")
    ]
    
    success_count = 0
    
    for file_path, description in required_files:
        path = Path(file_path)
        if path.exists():
            print(f"✅ {file_path:30s} - {description}")
            success_count += 1
        else:
            print(f"❌ {file_path:30s} - {description} (不存在)")
    
    print(f"\n📊 檔案檢查結果: {success_count}/{len(required_files)} 通過")
    return success_count == len(required_files)

def test_environment_config():
    """測試環境設定"""
    print("\n🔧 檢查環境設定...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env 檔案存在")
        
        # 檢查內容
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if "GEMINI_API_KEY=" in content:
                if "your_gemini_api_key_here" in content:
                    print("⚠️ 需要設定實際的 Gemini API 金鑰")
                    return False
                else:
                    print("✅ Gemini API 金鑰已設定")
                    return True
            else:
                print("❌ .env 檔案格式不正確")
                return False
                
        except Exception as e:
            print(f"❌ 讀取 .env 檔案失敗: {e}")
            return False
    
    elif env_example.exists():
        print("⚠️ .env 檔案不存在，但有範例檔案")
        print("💡 請複製 .env.example 為 .env 並設定 API 金鑰")
        return False
    
    else:
        print("❌ 環境設定檔案都不存在")
        return False

def test_module_imports():
    """測試模組匯入"""
    print("\n🔗 測試模組匯入...")
    
    modules_to_test = [
        ("server.config", "系統設定模組"),
        ("server.llm_client", "LLM 客戶端模組"),
        ("server.task_planner", "任務規劃器模組"),
        ("server.arm_simulator", "2D 模擬器模組"),
        ("server.inmoov_mujoco_simulator", "3D 模擬器模組"),
        ("server.arm_driver", "硬體驅動模組")
    ]
    
    success_count = 0
    
    for module_name, description in modules_to_test:
        try:
            importlib.import_module(module_name)
            print(f"✅ {module_name:30s} - {description}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name:30s} - {description} (匯入失敗: {e})")
        except Exception as e:
            print(f"⚠️ {module_name:30s} - {description} (警告: {e})")
            success_count += 1  # 警告不算失敗
    
    print(f"\n📊 模組匯入結果: {success_count}/{len(modules_to_test)} 通過")
    return success_count >= len(modules_to_test) * 0.8  # 80% 通過即可

def generate_test_report(results):
    """生成測試報告"""
    print("\n" + "="*80)
    print("📋 安裝驗證報告")
    print("="*80)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"📊 總體結果: {passed_tests}/{total_tests} 項測試通過")
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name:20s}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎊 恭喜！系統安裝完全正確")
        print("🚀 您可以開始使用 MCP 智慧機械手臂控制系統")
        print("💡 建議執行: python start_demo.py")
        
    elif passed_tests >= total_tests * 0.8:
        print("\n⚠️ 系統基本可用，但有些問題需要解決")
        print("💡 請檢查失敗的項目並修復")
        
    else:
        print("\n❌ 系統安裝不完整，需要修復多個問題")
        print("💡 建議重新安裝或檢查文件")
    
    print("\n📖 更多資訊請查看 README.md")

def main():
    """主函數"""
    print_banner()
    
    print("🔍 開始安裝驗證測試...")
    print("💡 這將檢查系統是否正確安裝和設定")
    
    # 執行各項測試
    test_results = {
        "Python 版本": test_python_version(),
        "必要套件": test_required_packages(),
        "專案結構": test_project_structure(),
        "環境設定": test_environment_config(),
        "模組匯入": test_module_imports()
    }
    
    # 生成測試報告
    generate_test_report(test_results)

if __name__ == "__main__":
    main()
