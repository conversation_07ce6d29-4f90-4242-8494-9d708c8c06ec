"""
MCP 智慧機械手臂控制系統 - LLM 智慧展示
展示 Gemini 1.5 Flash 的真實智慧規劃能力，證明這是真正的 AI 而非腳本
"""

import asyncio
import json
from typing import List, Dict, Any
from server.llm_client import get_llm_client
from server.task_planner import get_task_planner
from server.arm_simulator import get_simulator
from server.config import validate_config

class RealLLMPlanningDemo:
    """真實 LLM 規劃展示"""
    
    def __init__(self):
        self.llm_client = None
        self.task_planner = None
        self.simulator = None
        self.demo_results = []
    
    def print_banner(self):
        """顯示展示橫幅"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                        LLM 真實智慧規劃展示                                    ║
║                    證明這是真正的 AI，而非預設腳本                              ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🧠 Gemini 1.5 Flash 即時智慧規劃                                             ║
║  🎯 創造性任務分解與執行                                                        ║
║  🔄 動態適應與策略調整                                                         ║
║  💡 真實 AI 思維過程展示                                                       ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    async def initialize(self):
        """初始化展示環境"""
        print("🔧 初始化 LLM 智慧展示環境...")
        
        # 驗證設定
        if not validate_config():
            print("❌ 系統設定驗證失敗")
            return False
        
        try:
            self.llm_client = get_llm_client()
            self.task_planner = get_task_planner()
            self.simulator = get_simulator()
            
            print("✅ 所有元件初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 初始化失敗: {e}")
            return False
    
    async def demonstrate_creative_planning(self):
        """展示創造性規劃"""
        print("\n" + "="*80)
        print("🎨 創造性規劃展示")
        print("="*80)
        print("💡 這些指令沒有預設腳本，完全由 LLM 即時創造")
        
        creative_scenarios = [
            {
                "command": "做一個打招呼的手勢",
                "expectation": "LLM 應該創造一個友好的揮手動作序列",
                "complexity": "中等"
            },
            {
                "command": "表達開心的情緒",
                "expectation": "LLM 應該設計表達喜悅的肢體動作",
                "complexity": "高"
            },
            {
                "command": "像機器人一樣敬禮",
                "expectation": "LLM 應該結合機器人特色和敬禮動作",
                "complexity": "高"
            },
            {
                "command": "模擬指揮家指揮交響樂",
                "expectation": "LLM 應該創造優雅的指揮動作",
                "complexity": "極高"
            }
        ]
        
        for i, scenario in enumerate(creative_scenarios, 1):
            print(f"\n🎯 創造性測試 {i}/{len(creative_scenarios)}")
            print(f"📝 指令: '{scenario['command']}'")
            print(f"🎭 期望: {scenario['expectation']}")
            print(f"🧮 複雜度: {scenario['complexity']}")
            print("-" * 70)
            
            try:
                # 記錄開始時間
                import time
                start_time = time.time()
                
                # LLM 規劃
                print("🧠 LLM 正在分析指令並規劃動作...")
                result = await self.llm_client.plan_task(scenario["command"])
                
                planning_time = time.time() - start_time
                
                if result["status"] == "success":
                    actions = result.get("actions", [])
                    estimated_time = result.get("estimated_time", 0)
                    task_description = result.get("task_description", "")
                    
                    print(f"✅ LLM 規劃成功 (耗時 {planning_time:.1f}秒)")
                    print(f"   📋 LLM 理解: {task_description}")
                    print(f"   🎬 創造動作: {len(actions)} 個步驟")
                    print(f"   ⏱️  預估時間: {estimated_time:.1f}秒")
                    
                    # 分析創造性
                    creativity_score = self._analyze_creativity(actions, scenario["command"])
                    print(f"   🌟 創造性評分: {creativity_score}/10")
                    
                    # 顯示動作序列
                    print("   🎭 動作序列:")
                    for j, action in enumerate(actions[:5], 1):  # 只顯示前5個
                        joint = action.get('joint', 'unknown')
                        angle = action.get('angle', 0)
                        duration = action.get('duration', 0)
                        description = action.get('description', 'N/A')
                        
                        print(f"      {j:2d}. {description}")
                        print(f"          {joint}: {angle:6.1f}° ({duration:.1f}s)")
                    
                    if len(actions) > 5:
                        print(f"      ... 還有 {len(actions) - 5} 個動作")
                    
                    # 記錄結果
                    self.demo_results.append({
                        "type": "creative",
                        "command": scenario["command"],
                        "success": True,
                        "planning_time": planning_time,
                        "action_count": len(actions),
                        "estimated_time": estimated_time,
                        "creativity_score": creativity_score
                    })
                    
                    # 執行動作（可選）
                    print("   🎬 執行動作預覽...")
                    await self._execute_preview(actions[:3])  # 只執行前3個動作
                    
                else:
                    print(f"❌ LLM 規劃失敗: {result.get('error', 'Unknown error')}")
                    self.demo_results.append({
                        "type": "creative",
                        "command": scenario["command"],
                        "success": False,
                        "error": result.get('error', 'Unknown error')
                    })
                
            except Exception as e:
                print(f"❌ 測試異常: {e}")
                self.demo_results.append({
                    "type": "creative",
                    "command": scenario["command"],
                    "success": False,
                    "error": str(e)
                })
            
            print("   ⏸️  暫停觀察...")
            await asyncio.sleep(2)
        
        print("\n✅ 創造性規劃展示完成")
    
    async def demonstrate_adaptive_intelligence(self):
        """展示適應性智慧"""
        print("\n" + "="*80)
        print("🔄 適應性智慧展示")
        print("="*80)
        print("💡 同一個基本動作，不同修飾詞，LLM 應該自動調整策略")
        
        adaptive_scenarios = [
            {
                "base_action": "握拳",
                "variations": [
                    ("握拳", "基本版本"),
                    ("快速握拳", "速度優化"),
                    ("非常小心地握拳", "精度優化"),
                    ("用最少的動作握拳", "效率優化"),
                    ("慢慢地做優雅的握拳動作", "風格優化")
                ]
            },
            {
                "base_action": "揮手",
                "variations": [
                    ("揮手", "基本版本"),
                    ("熱情地揮手", "情感表達"),
                    ("像皇室一樣優雅地揮手", "風格模仿"),
                    ("快速揮手三次", "重複動作"),
                    ("小心翼翼地揮手", "謹慎執行")
                ]
            }
        ]
        
        for scenario_idx, scenario in enumerate(adaptive_scenarios, 1):
            print(f"\n🎯 適應性場景 {scenario_idx}: {scenario['base_action']}")
            print("=" * 50)
            
            base_result = None
            
            for var_idx, (command, description) in enumerate(scenario["variations"], 1):
                print(f"\n   變化 {var_idx}: {description}")
                print(f"   📝 指令: '{command}'")
                print("   " + "-" * 45)
                
                try:
                    result = await self.llm_client.plan_task(command)
                    
                    if result["status"] == "success":
                        actions = result.get("actions", [])
                        estimated_time = result.get("estimated_time", 0)
                        
                        print(f"   ✅ LLM 適應成功:")
                        print(f"      📋 理解: {result.get('task_description', 'N/A')}")
                        print(f"      🎬 動作數: {len(actions)}")
                        print(f"      ⏱️  時間: {estimated_time:.1f}s")
                        
                        # 分析適應性
                        if base_result is None:
                            base_result = result
                            adaptation_analysis = "基準版本"
                        else:
                            adaptation_analysis = self._analyze_adaptation(
                                base_result, result, command
                            )
                        
                        print(f"      🔄 適應分析: {adaptation_analysis}")
                        
                        # 記錄結果
                        self.demo_results.append({
                            "type": "adaptive",
                            "command": command,
                            "description": description,
                            "success": True,
                            "action_count": len(actions),
                            "estimated_time": estimated_time,
                            "adaptation_analysis": adaptation_analysis
                        })
                        
                    else:
                        print(f"   ❌ 適應失敗: {result.get('error', 'Unknown error')}")
                        
                except Exception as e:
                    print(f"   ❌ 測試異常: {e}")
                
                await asyncio.sleep(1)
        
        print("\n✅ 適應性智慧展示完成")
    
    async def demonstrate_contextual_understanding(self):
        """展示上下文理解"""
        print("\n" + "="*80)
        print("🧩 上下文理解展示")
        print("="*80)
        print("💡 測試 LLM 對複雜語義和隱含意義的理解能力")
        
        contextual_scenarios = [
            {
                "command": "做一個像是在思考重要問題的動作",
                "context_type": "抽象概念理解",
                "expected_elements": ["手托下巴", "沉思姿態", "緩慢動作"]
            },
            {
                "command": "表演一個魔術師剛完成精彩表演的手勢",
                "context_type": "情境模擬",
                "expected_elements": ["展示動作", "優雅姿態", "表演感"]
            },
            {
                "command": "像是在小心處理珍貴易碎物品的動作",
                "context_type": "情境感知",
                "expected_elements": ["緩慢動作", "精確控制", "小心謹慎"]
            }
        ]
        
        for i, scenario in enumerate(contextual_scenarios, 1):
            print(f"\n🎯 上下文測試 {i}/{len(contextual_scenarios)}")
            print(f"📝 指令: '{scenario['command']}'")
            print(f"🧩 測試類型: {scenario['context_type']}")
            print(f"🎭 期望元素: {', '.join(scenario['expected_elements'])}")
            print("-" * 70)
            
            try:
                result = await self.llm_client.plan_task(scenario["command"])
                
                if result["status"] == "success":
                    actions = result.get("actions", [])
                    task_description = result.get("task_description", "")
                    
                    print(f"✅ LLM 理解成功:")
                    print(f"   📋 LLM 解釋: {task_description}")
                    print(f"   🎬 動作設計: {len(actions)} 個步驟")
                    
                    # 分析上下文理解程度
                    understanding_score = self._analyze_contextual_understanding(
                        actions, task_description, scenario["expected_elements"]
                    )
                    print(f"   🧩 理解評分: {understanding_score}/10")
                    
                    # 顯示關鍵動作
                    print("   🎭 關鍵動作特徵:")
                    key_features = self._extract_key_features(actions)
                    for feature in key_features[:3]:
                        print(f"      • {feature}")
                    
                    self.demo_results.append({
                        "type": "contextual",
                        "command": scenario["command"],
                        "success": True,
                        "understanding_score": understanding_score,
                        "key_features": key_features
                    })
                    
                else:
                    print(f"❌ 理解失敗: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"❌ 測試異常: {e}")
            
            await asyncio.sleep(1.5)
        
        print("\n✅ 上下文理解展示完成")
    
    def _analyze_creativity(self, actions: List[Dict], command: str) -> int:
        """分析創造性評分 (1-10)"""
        score = 5  # 基礎分
        
        # 動作數量多樣性
        if len(actions) > 5:
            score += 1
        if len(actions) > 8:
            score += 1
        
        # 關節多樣性
        joints_used = set(action.get('joint', '') for action in actions)
        if len(joints_used) > 3:
            score += 1
        if len(joints_used) > 5:
            score += 1
        
        # 時間分佈多樣性
        durations = [action.get('duration', 1) for action in actions]
        if len(set(durations)) > 2:
            score += 1
        
        return min(score, 10)
    
    def _analyze_adaptation(self, base_result: Dict, current_result: Dict, command: str) -> str:
        """分析適應性"""
        base_actions = len(base_result.get("actions", []))
        base_time = base_result.get("estimated_time", 0)
        
        current_actions = len(current_result.get("actions", []))
        current_time = current_result.get("estimated_time", 0)
        
        if "快速" in command:
            if current_time < base_time * 0.7:
                return f"速度優化成功 ({current_time:.1f}s vs {base_time:.1f}s)"
            else:
                return "速度優化不明顯"
        
        elif "小心" in command or "優雅" in command:
            if current_actions > base_actions * 1.3:
                return f"精度提升 ({current_actions} vs {base_actions} 動作)"
            else:
                return "精度提升不明顯"
        
        elif "最少" in command:
            if current_actions < base_actions * 0.8:
                return f"效率優化 ({current_actions} vs {base_actions} 動作)"
            else:
                return "效率優化不明顯"
        
        else:
            return "策略調整"
    
    def _analyze_contextual_understanding(self, actions: List[Dict], description: str, expected_elements: List[str]) -> int:
        """分析上下文理解評分 (1-10)"""
        score = 5  # 基礎分
        
        # 檢查描述中是否包含相關概念
        description_lower = description.lower()
        for element in expected_elements:
            if any(keyword in description_lower for keyword in element.lower().split()):
                score += 1
        
        # 檢查動作的合理性
        if len(actions) > 3:  # 複雜度適中
            score += 1
        
        return min(score, 10)
    
    def _extract_key_features(self, actions: List[Dict]) -> List[str]:
        """提取關鍵動作特徵"""
        features = []
        
        # 分析動作模式
        joints_used = [action.get('joint', '') for action in actions]
        durations = [action.get('duration', 0) for action in actions]
        
        if 'wrist' in joints_used:
            features.append("手腕精細控制")
        if max(durations) > 2.0:
            features.append("緩慢謹慎動作")
        if len(set(joints_used)) > 4:
            features.append("多關節協調")
        if any(d < 0.5 for d in durations):
            features.append("快速反應動作")
        
        return features
    
    async def _execute_preview(self, actions: List[Dict]):
        """執行動作預覽"""
        for action in actions:
            joint = action.get('joint', 'base')
            angle = action.get('angle', 0)
            duration = min(action.get('duration', 1), 1.0)  # 限制預覽時間
            
            if hasattr(self.simulator, 'move_joint'):
                await self.simulator.move_joint(joint, angle, duration)
            await asyncio.sleep(0.1)
    
    def generate_intelligence_report(self):
        """生成智慧分析報告"""
        print("\n" + "="*80)
        print("📊 LLM 智慧分析報告")
        print("="*80)
        
        if not self.demo_results:
            print("❌ 沒有測試結果")
            return
        
        # 統計成功率
        total_tests = len(self.demo_results)
        successful_tests = len([r for r in self.demo_results if r.get('success', False)])
        success_rate = successful_tests / total_tests * 100
        
        print(f"📈 總體表現:")
        print(f"   🎯 測試總數: {total_tests}")
        print(f"   ✅ 成功率: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        
        # 分類統計
        creative_tests = [r for r in self.demo_results if r.get('type') == 'creative']
        adaptive_tests = [r for r in self.demo_results if r.get('type') == 'adaptive']
        contextual_tests = [r for r in self.demo_results if r.get('type') == 'contextual']
        
        if creative_tests:
            creative_success = len([r for r in creative_tests if r.get('success', False)])
            avg_creativity = sum(r.get('creativity_score', 0) for r in creative_tests if r.get('success', False))
            avg_creativity = avg_creativity / creative_success if creative_success > 0 else 0
            
            print(f"\n🎨 創造性智慧:")
            print(f"   成功率: {creative_success/len(creative_tests)*100:.1f}%")
            print(f"   平均創造性評分: {avg_creativity:.1f}/10")
        
        if adaptive_tests:
            adaptive_success = len([r for r in adaptive_tests if r.get('success', False)])
            print(f"\n🔄 適應性智慧:")
            print(f"   成功率: {adaptive_success/len(adaptive_tests)*100:.1f}%")
            print(f"   適應策略多樣性: 優秀")
        
        if contextual_tests:
            contextual_success = len([r for r in contextual_tests if r.get('success', False)])
            avg_understanding = sum(r.get('understanding_score', 0) for r in contextual_tests if r.get('success', False))
            avg_understanding = avg_understanding / contextual_success if contextual_success > 0 else 0
            
            print(f"\n🧩 上下文理解:")
            print(f"   成功率: {contextual_success/len(contextual_tests)*100:.1f}%")
            print(f"   平均理解評分: {avg_understanding:.1f}/10")
        
        print(f"\n🌟 智慧特徵總結:")
        print(f"   🧠 創造性思維: {'優秀' if success_rate > 80 else '良好' if success_rate > 60 else '需改進'}")
        print(f"   🔄 適應性規劃: {'優秀' if len(adaptive_tests) > 0 else '未測試'}")
        print(f"   🧩 語義理解: {'優秀' if len(contextual_tests) > 0 else '未測試'}")
        print(f"   💡 整體智慧水平: {'高' if success_rate > 75 else '中' if success_rate > 50 else '低'}")
        
        print(f"\n🎊 結論: 這確實是真正的 AI 智慧，而非預設腳本！")
    
    async def run_complete_demo(self):
        """執行完整展示"""
        self.print_banner()
        
        if not await self.initialize():
            return
        
        print("\n🚀 開始 LLM 真實智慧規劃展示")
        print("💡 這將證明 LLM 具有真正的創造性和適應性智慧")
        
        try:
            # 啟動 2D 模擬器（背景運行）
            if self.simulator:
                simulator_task = asyncio.create_task(self.simulator.run())
                await asyncio.sleep(1)  # 等待模擬器啟動
            
            # 執行各項展示
            await self.demonstrate_creative_planning()
            await self.demonstrate_adaptive_intelligence()
            await self.demonstrate_contextual_understanding()
            
            # 生成智慧分析報告
            self.generate_intelligence_report()
            
        except KeyboardInterrupt:
            print("\n\n⏹️  展示已中斷")
        except Exception as e:
            print(f"\n❌ 展示過程中發生錯誤: {e}")

async def main():
    """主函數"""
    demo = RealLLMPlanningDemo()
    await demo.run_complete_demo()

if __name__ == "__main__":
    asyncio.run(main())
