"""
MCP 智慧機械手臂控制系統 - 硬體驅動程式
提供與真實機械手臂硬體通訊的介面（目前為模擬實作）
"""

import asyncio
import serial
import time
from typing import Dict, List, Optional, Any
from loguru import logger
from .config import get_config

class ServoMotor:
    """伺服馬達類別"""
    
    def __init__(self, name: str, pin: int, min_angle: float = 0, max_angle: float = 180):
        self.name = name
        self.pin = pin
        self.min_angle = min_angle
        self.max_angle = max_angle
        self.current_angle = 90.0  # 預設中間位置
        self.target_angle = 90.0
        self.moving = False
        self.speed = 60.0  # 度/秒
    
    def set_angle(self, angle: float):
        """設定目標角度"""
        self.target_angle = max(self.min_angle, min(self.max_angle, angle))
        self.moving = abs(self.target_angle - self.current_angle) > 1.0
    
    def update(self, dt: float):
        """更新馬達狀態"""
        if self.moving:
            diff = self.target_angle - self.current_angle
            if abs(diff) < 1.0:
                self.current_angle = self.target_angle
                self.moving = False
            else:
                direction = 1 if diff > 0 else -1
                self.current_angle += direction * self.speed * dt
                self.current_angle = max(self.min_angle, min(self.max_angle, self.current_angle))

class ArmHardwareDriver:
    """機械手臂硬體驅動程式"""
    
    def __init__(self, port: str = "COM3", baudrate: int = 115200):
        self.config = get_config()
        self.port = port
        self.baudrate = baudrate
        self.serial_connection = None
        self.connected = False
        
        # 建立伺服馬達映射
        self.servos = self._create_servo_mapping()
        
        # 狀態監控
        self.last_update = time.time()
        self.update_frequency = 50  # Hz
        
        logger.info("✅ 機械手臂硬體驅動程式初始化完成")
    
    def _create_servo_mapping(self) -> Dict[str, ServoMotor]:
        """建立伺服馬達映射"""
        servos = {
            'base': ServoMotor('base', 3, 0, 180),
            'shoulder': ServoMotor('shoulder', 5, 0, 180),
            'elbow': ServoMotor('elbow', 6, 0, 180),
            'wrist': ServoMotor('wrist', 9, 0, 180),
            'thumb': ServoMotor('thumb', 10, 0, 90),
            'index': ServoMotor('index', 11, 0, 90),
            'middle': ServoMotor('middle', 12, 0, 90),
            'ring': ServoMotor('ring', 13, 0, 90),
            'pinky': ServoMotor('pinky', 14, 0, 90)
        }
        return servos
    
    async def connect(self) -> bool:
        """連接到硬體"""
        try:
            # 模擬連接（實際應用中會開啟串列埠）
            logger.info(f"🔌 嘗試連接到硬體: {self.port} @ {self.baudrate}")
            
            # 模擬連接延遲
            await asyncio.sleep(1.0)
            
            # 在真實環境中，這裡會是：
            # self.serial_connection = serial.Serial(self.port, self.baudrate, timeout=1)
            
            self.connected = True
            logger.info("✅ 硬體連接成功")
            
            # 初始化所有伺服馬達到中間位置
            await self.initialize_servos()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 硬體連接失敗: {e}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """斷開硬體連接"""
        if self.serial_connection:
            self.serial_connection.close()
            self.serial_connection = None
        
        self.connected = False
        logger.info("🔌 硬體連接已斷開")
    
    async def initialize_servos(self):
        """初始化所有伺服馬達"""
        logger.info("🔧 初始化伺服馬達...")
        
        for servo in self.servos.values():
            servo.set_angle(90.0)  # 設定到中間位置
            await self._send_servo_command(servo)
        
        logger.info("✅ 伺服馬達初始化完成")
    
    async def move_joint(self, joint_name: str, angle: float, duration: float = 1.0):
        """移動指定關節"""
        if not self.connected:
            logger.warning("⚠️ 硬體未連接")
            return False
        
        if joint_name not in self.servos:
            logger.warning(f"⚠️ 未知關節: {joint_name}")
            return False
        
        servo = self.servos[joint_name]
        
        # 計算移動速度
        angle_diff = abs(angle - servo.current_angle)
        servo.speed = angle_diff / duration if duration > 0 else 60.0
        
        # 設定目標角度
        servo.set_angle(angle)
        
        logger.info(f"🎯 移動關節 {joint_name}: {servo.current_angle:.1f}° → {angle:.1f}° ({duration:.1f}s)")
        
        # 發送命令到硬體
        await self._send_servo_command(servo)
        
        # 等待移動完成
        while servo.moving:
            await asyncio.sleep(0.02)  # 50Hz 更新頻率
            servo.update(0.02)
        
        return True
    
    async def _send_servo_command(self, servo: ServoMotor):
        """發送伺服馬達命令到硬體"""
        try:
            # 模擬發送命令（實際應用中會透過串列埠發送）
            command = f"#{servo.pin}P{int(servo.target_angle * 10)}T{int(servo.speed * 10)}\r"
            
            # 在真實環境中，這裡會是：
            # if self.serial_connection:
            #     self.serial_connection.write(command.encode())
            
            logger.debug(f"📡 發送命令: {command.strip()}")
            
            # 模擬命令執行時間
            await asyncio.sleep(0.01)
            
        except Exception as e:
            logger.error(f"❌ 發送伺服馬達命令失敗: {e}")
    
    async def get_joint_angles(self) -> Dict[str, float]:
        """取得所有關節角度"""
        angles = {}
        for name, servo in self.servos.items():
            angles[name] = servo.current_angle
        return angles
    
    async def emergency_stop(self):
        """緊急停止所有馬達"""
        logger.warning("🚨 緊急停止所有馬達")
        
        for servo in self.servos.values():
            servo.moving = False
            servo.target_angle = servo.current_angle
        
        # 發送停止命令到硬體
        if self.connected:
            # 在真實環境中，這裡會發送緊急停止命令
            logger.info("📡 發送緊急停止命令到硬體")
    
    async def calibrate_joint(self, joint_name: str) -> bool:
        """校準指定關節"""
        if joint_name not in self.servos:
            logger.warning(f"⚠️ 未知關節: {joint_name}")
            return False
        
        servo = self.servos[joint_name]
        
        logger.info(f"🔧 開始校準關節: {joint_name}")
        
        # 校準程序：移動到最小位置、最大位置、中間位置
        calibration_positions = [servo.min_angle, servo.max_angle, 90.0]
        
        for position in calibration_positions:
            await self.move_joint(joint_name, position, 2.0)
            await asyncio.sleep(1.0)
        
        logger.info(f"✅ 關節校準完成: {joint_name}")
        return True
    
    async def run_self_test(self) -> Dict[str, Any]:
        """執行系統自檢"""
        logger.info("🔍 開始系統自檢...")
        
        test_results = {
            "connection": self.connected,
            "servos": {},
            "overall": True
        }
        
        if not self.connected:
            test_results["overall"] = False
            return test_results
        
        # 測試每個伺服馬達
        for name, servo in self.servos.items():
            logger.info(f"🔧 測試伺服馬達: {name}")
            
            try:
                # 小幅度移動測試
                original_angle = servo.current_angle
                test_angle = original_angle + 10 if original_angle < servo.max_angle - 10 else original_angle - 10
                
                await self.move_joint(name, test_angle, 1.0)
                await asyncio.sleep(0.5)
                await self.move_joint(name, original_angle, 1.0)
                
                test_results["servos"][name] = {
                    "status": "pass",
                    "current_angle": servo.current_angle,
                    "range": f"{servo.min_angle}-{servo.max_angle}"
                }
                
            except Exception as e:
                logger.error(f"❌ 伺服馬達測試失敗: {name} - {e}")
                test_results["servos"][name] = {
                    "status": "fail",
                    "error": str(e)
                }
                test_results["overall"] = False
        
        status = "✅ 通過" if test_results["overall"] else "❌ 失敗"
        logger.info(f"🔍 系統自檢完成: {status}")
        
        return test_results
    
    async def update_loop(self):
        """硬體狀態更新循環"""
        while self.connected:
            current_time = time.time()
            dt = current_time - self.last_update
            
            # 更新所有伺服馬達狀態
            for servo in self.servos.values():
                servo.update(dt)
            
            self.last_update = current_time
            await asyncio.sleep(1.0 / self.update_frequency)

# 全域硬體驅動實例
_hardware_driver = None

def get_hardware_driver() -> ArmHardwareDriver:
    """取得硬體驅動實例"""
    global _hardware_driver
    if _hardware_driver is None:
        _hardware_driver = ArmHardwareDriver()
    return _hardware_driver

if __name__ == "__main__":
    # 測試硬體驅動程式
    async def test_hardware():
        print("🔧 機械手臂硬體驅動程式測試")
        print("=" * 50)
        
        driver = get_hardware_driver()
        
        # 連接硬體
        if await driver.connect():
            # 執行自檢
            test_results = await driver.run_self_test()
            print(f"🔍 自檢結果: {'通過' if test_results['overall'] else '失敗'}")
            
            # 測試動作
            test_moves = [
                ("shoulder", 45, 2.0),
                ("elbow", 90, 1.5),
                ("wrist", 135, 1.0),
                ("thumb", 45, 0.5)
            ]
            
            for joint, angle, duration in test_moves:
                print(f"🎯 移動 {joint} 到 {angle}°")
                await driver.move_joint(joint, angle, duration)
                await asyncio.sleep(0.5)
            
            # 取得關節角度
            angles = await driver.get_joint_angles()
            print(f"📊 當前關節角度: {angles}")
            
            # 斷開連接
            await driver.disconnect()
        else:
            print("❌ 硬體連接失敗")
    
    # 執行測試
    asyncio.run(test_hardware())
