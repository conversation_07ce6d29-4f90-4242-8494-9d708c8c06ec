"""
MCP 智慧機械手臂控制系統 - 快速展示
展示 2D 模擬器的基本功能
"""

import pygame
import sys
import asyncio
import time
from server.arm_simulator import ArmSimulator2D

def print_banner():
    """顯示展示橫幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    MCP 智慧機械手臂控制系統                                    ║
║                        🎮 2D 模擬器展示 🎮                                     ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  這個展示將向您展示 2D 機械手臂模擬器的基本功能                                  ║
║  包括關節控制、動作序列和視覺化效果                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

async def demo_sequence():
    """執行展示動作序列"""
    print("🤖 初始化 2D 機械手臂模擬器...")
    
    # 初始化模擬器
    simulator = ArmSimulator2D()
    
    print("✅ 模擬器初始化完成")
    print("🎬 開始執行展示動作序列...")
    
    # 展示動作序列
    demo_actions = [
        {
            "name": "👋 打招呼手勢",
            "description": "機械手臂做出友好的打招呼動作",
            "actions": [
                ("shoulder", 45, 2.0),
                ("elbow", -90, 1.5),
                ("wrist", 30, 1.0),
                ("wrist", -30, 0.5),
                ("wrist", 30, 0.5),
                ("wrist", 0, 1.0)
            ]
        },
        {
            "name": "✊ 握拳動作",
            "description": "展示手指關節的精密控制",
            "actions": [
                ("thumb", 90, 1.0),
                ("index", 90, 1.0),
                ("middle", 90, 1.0),
                ("ring", 90, 1.0),
                ("pinky", 90, 1.0)
            ]
        },
        {
            "name": "🤚 張開手掌",
            "description": "從握拳狀態張開手掌",
            "actions": [
                ("thumb", 0, 1.0),
                ("index", 0, 1.0),
                ("middle", 0, 1.0),
                ("ring", 0, 1.0),
                ("pinky", 0, 1.0)
            ]
        },
        {
            "name": "🔄 旋轉展示",
            "description": "展示底座旋轉功能",
            "actions": [
                ("base", 90, 2.0),
                ("base", -90, 2.0),
                ("base", 0, 2.0)
            ]
        },
        {
            "name": "🏠 回到初始位置",
            "description": "所有關節回到初始狀態",
            "actions": [
                ("shoulder", 0, 2.0),
                ("elbow", 0, 2.0),
                ("wrist", 0, 1.0)
            ]
        }
    ]
    
    # 執行展示動作
    for i, demo in enumerate(demo_actions, 1):
        print(f"\n🎯 展示 {i}/{len(demo_actions)}: {demo['name']}")
        print(f"   📝 {demo['description']}")
        
        for joint, angle, duration in demo['actions']:
            print(f"   🎮 移動 {joint} 到 {angle}° (耗時 {duration}s)")
            await simulator.move_joint(joint, angle, duration)
            
        print(f"   ✅ {demo['name']} 完成")
        await asyncio.sleep(1)  # 短暫暫停
    
    print("\n🎊 所有展示動作完成！")
    print("💡 您可以看到 2D 模擬器視窗中的機械手臂動作")
    print("🖱️  點擊視窗關閉按鈕或按 ESC 鍵退出")
    
    # 保持模擬器運行
    try:
        simulator.run()
    except KeyboardInterrupt:
        print("\n👋 展示已結束")
    except Exception as e:
        print(f"\n❌ 展示過程中發生錯誤: {e}")

def main():
    """主函數"""
    print_banner()
    
    print("🚀 準備啟動 2D 機械手臂模擬器展示...")
    print("💡 這將開啟一個視覺化視窗，展示機械手臂的各種動作")
    
    try:
        # 運行展示
        asyncio.run(demo_sequence())
        
    except KeyboardInterrupt:
        print("\n\n👋 展示已中斷")
    except Exception as e:
        print(f"\n❌ 展示啟動失敗: {e}")
        print("💡 請確保已安裝 pygame 套件: pip install pygame")

if __name__ == "__main__":
    main()
