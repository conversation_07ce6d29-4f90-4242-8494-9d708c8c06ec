"""
MCP 智慧機械手臂控制系統 - Gemini LLM 客戶端
負責與 Google Gemini 1.5 Flash 模型進行智慧對話和任務規劃
"""

import json
import asyncio
from typing import Dict, List, Optional, Any
import google.generativeai as genai
from loguru import logger
from .config import get_config

class GeminiLLMClient:
    """Gemini LLM 客戶端類別"""
    
    def __init__(self):
        self.config = get_config()
        self.model = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化 Gemini 客戶端"""
        try:
            if not self.config.gemini_api_key:
                raise ValueError("未設定 GEMINI_API_KEY")
            
            genai.configure(api_key=self.config.gemini_api_key)
            self.model = genai.GenerativeModel(self.config.gemini_model)
            logger.info(f"✅ Gemini LLM 客戶端初始化成功 - 模型: {self.config.gemini_model}")
            
        except Exception as e:
            logger.error(f"❌ Gemini LLM 客戶端初始化失敗: {e}")
            raise
    
    async def plan_task(self, command: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        使用 LLM 規劃機械手臂任務
        
        Args:
            command: 自然語言指令
            context: 額外的上下文資訊
            
        Returns:
            包含任務規劃結果的字典
        """
        try:
            # 建構提示詞
            prompt = self._build_task_planning_prompt(command, context)
            
            # 呼叫 Gemini API
            response = await self._generate_response(prompt)
            
            # 解析回應
            task_plan = self._parse_task_response(response, command)
            
            logger.info(f"🧠 LLM 任務規劃完成: {command}")
            return task_plan
            
        except Exception as e:
            logger.error(f"❌ LLM 任務規劃失敗: {e}")
            return self._create_error_response(str(e))
    
    def _build_task_planning_prompt(self, command: str, context: Optional[Dict] = None) -> str:
        """建構任務規劃提示詞"""
        
        base_prompt = f"""
你是一個專業的機械手臂控制專家，負責將自然語言指令轉換為具體的機械手臂動作序列。

機械手臂規格：
- 11 自由度 InMoov 機器人
- 關節：base(底座), shoulder(肩膀), elbow(手肘), wrist(手腕), thumb(拇指), index(食指), middle(中指), ring(無名指), pinky(小指)
- 角度範圍：-180° 到 +180°
- 最大扭矩：15.0 kg⋅cm

用戶指令："{command}"

請規劃一個詳細的動作序列，包含：
1. 每個動作的關節名稱
2. 目標角度（度）
3. 執行時間（秒）
4. 動作描述

請以 JSON 格式回應，格式如下：
{{
    "task_description": "任務描述",
    "estimated_time": 總執行時間,
    "actions": [
        {{
            "joint": "關節名稱",
            "angle": 角度值,
            "duration": 執行時間,
            "description": "動作描述"
        }}
    ],
    "safety_notes": ["安全注意事項"]
}}

請確保動作序列是安全、合理且能達成目標的。
"""
        
        if context:
            base_prompt += f"\n額外上下文：{json.dumps(context, ensure_ascii=False, indent=2)}"
        
        return base_prompt
    
    async def _generate_response(self, prompt: str) -> str:
        """生成 LLM 回應"""
        try:
            # 使用同步方法，因為 google-generativeai 目前不支援 async
            response = self.model.generate_content(prompt)
            return response.text
            
        except Exception as e:
            logger.error(f"❌ LLM 回應生成失敗: {e}")
            raise
    
    def _parse_task_response(self, response: str, original_command: str) -> Dict[str, Any]:
        """解析 LLM 任務回應"""
        try:
            # 嘗試解析 JSON
            # 移除可能的 markdown 格式
            clean_response = response.strip()
            if clean_response.startswith("```json"):
                clean_response = clean_response[7:]
            if clean_response.endswith("```"):
                clean_response = clean_response[:-3]
            
            task_plan = json.loads(clean_response.strip())
            
            # 驗證必要欄位
            required_fields = ["task_description", "estimated_time", "actions"]
            for field in required_fields:
                if field not in task_plan:
                    raise ValueError(f"缺少必要欄位: {field}")
            
            # 添加元資料
            task_plan["original_command"] = original_command
            task_plan["status"] = "success"
            task_plan["llm_model"] = self.config.gemini_model
            
            return task_plan
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON 解析失敗: {e}")
            logger.error(f"原始回應: {response}")
            return self._create_fallback_response(original_command)
        
        except Exception as e:
            logger.error(f"❌ 任務回應解析失敗: {e}")
            return self._create_error_response(str(e))
    
    def _create_fallback_response(self, command: str) -> Dict[str, Any]:
        """建立備用回應（當 LLM 回應無法解析時）"""
        return {
            "status": "fallback",
            "original_command": command,
            "task_description": f"執行指令: {command}",
            "estimated_time": 5.0,
            "actions": [
                {
                    "joint": "base",
                    "angle": 0.0,
                    "duration": 2.0,
                    "description": "回到初始位置"
                },
                {
                    "joint": "shoulder",
                    "angle": 30.0,
                    "duration": 2.0,
                    "description": "肩膀動作"
                },
                {
                    "joint": "elbow",
                    "angle": -45.0,
                    "duration": 1.0,
                    "description": "手肘動作"
                }
            ],
            "safety_notes": ["這是備用動作序列"],
            "llm_model": self.config.gemini_model
        }
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """建立錯誤回應"""
        return {
            "status": "error",
            "error": error_message,
            "task_description": "任務執行失敗",
            "estimated_time": 0.0,
            "actions": [],
            "safety_notes": ["發生錯誤，請檢查系統狀態"],
            "llm_model": self.config.gemini_model
        }

# 全域 LLM 客戶端實例
_llm_client = None

def get_llm_client() -> GeminiLLMClient:
    """取得 LLM 客戶端實例"""
    global _llm_client
    if _llm_client is None:
        _llm_client = GeminiLLMClient()
    return _llm_client

if __name__ == "__main__":
    # 測試 LLM 客戶端
    async def test_llm():
        print("🧠 Gemini LLM 客戶端測試")
        print("=" * 50)
        
        client = get_llm_client()
        
        test_commands = [
            "做一個打招呼的手勢",
            "握拳",
            "指向前方"
        ]
        
        for command in test_commands:
            print(f"\n🎯 測試指令: {command}")
            result = await client.plan_task(command)
            print(f"📋 規劃結果: {result['status']}")
            if result['status'] == 'success':
                print(f"⏱️  預估時間: {result['estimated_time']}秒")
                print(f"🎬 動作數量: {len(result['actions'])}")
    
    # 執行測試
    asyncio.run(test_llm())
